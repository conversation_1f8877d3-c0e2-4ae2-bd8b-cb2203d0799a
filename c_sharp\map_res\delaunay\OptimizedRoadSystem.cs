using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// 优化的道路系统 - 使用道路段+算法查询替代HashSet存储
/// </summary>
public partial class OptimizedRoadSystem : RefCounted
{
    /// <summary>
    /// 道路段数据结构
    /// </summary>
    public class RoadSegment
    {
        public Vector2I StartPoint { get; set; }
        public Vector2I EndPoint { get; set; }
        public int Width { get; set; } = 1;
        public RoadType Type { get; set; }
        public bool IsHorizontal => StartPoint.Y == EndPoint.Y;
        public bool IsVertical => StartPoint.X == EndPoint.X;
        
        // 预计算的边界框，用于快速排除
        public int MinX { get; set; }
        public int MaxX { get; set; }
        public int MinY { get; set; }
        public int MaxY { get; set; }
        
        public void CalculateBounds()
        {
            MinX = Math.Min(StartPoint.X, EndPoint.X) - Width / 2;
            MaxX = Math.Max(StartPoint.X, EndPoint.X) + Width / 2;
            MinY = Math.Min(StartPoint.Y, EndPoint.Y) - Width / 2;
            MaxY = Math.Max(StartPoint.Y, EndPoint.Y) + Width / 2;
        }
    }

    public enum RoadType
    {
        MainRoad,    // 主干道
        LocalRoad,   // 小路
        Highway      // 高速路
    }

    /// <summary>
    /// 空间网格索引
    /// </summary>
    public class SpatialGrid
    {
        private Dictionary<Vector2I, List<RoadSegment>> grid = new();
        private int cellSize;

        public SpatialGrid(int cellSize = 64)
        {
            this.cellSize = cellSize;
        }

        public void AddRoadSegment(RoadSegment segment)
        {
            // 计算道路段覆盖的网格范围
            int startGridX = segment.MinX / cellSize;
            int endGridX = segment.MaxX / cellSize;
            int startGridY = segment.MinY / cellSize;
            int endGridY = segment.MaxY / cellSize;

            // 将道路段添加到所有相关网格
            for (int gx = startGridX; gx <= endGridX; gx++)
            {
                for (int gy = startGridY; gy <= endGridY; gy++)
                {
                    var gridCoord = new Vector2I(gx, gy);
                    if (!grid.ContainsKey(gridCoord))
                    {
                        grid[gridCoord] = new List<RoadSegment>();
                    }
                    grid[gridCoord].Add(segment);
                }
            }
        }

        public List<RoadSegment> GetNearbySegments(Vector2I worldCoord)
        {
            var gridCoord = new Vector2I(worldCoord.X / cellSize, worldCoord.Y / cellSize);
            return grid.GetValueOrDefault(gridCoord, new List<RoadSegment>());
        }

        public void Clear()
        {
            grid.Clear();
        }
    }

    // 数据存储 - 分离主路和小路
    private List<RoadSegment> mainRoadSegments = new();     // 主干道存储
    private List<RoadSegment> localRoadSegments = new();    // 小路存储
    private List<RoadSegment> allRoadSegments = new();      // 所有道路的合并视图（用于兼容性）

    private SpatialGrid mainRoadSpatialIndex = new(64);     // 主路空间索引
    private SpatialGrid localRoadSpatialIndex = new(32);    // 小路空间索引（更小的网格）
    private SpatialGrid spatialIndex = new(64);             // 统一空间索引（用于兼容性）

    private Dictionary<Vector2I, bool> queryCache = new();      // 统一查询缓存
    private Dictionary<Vector2I, bool> mainRoadQueryCache = new();  // 主路查询缓存
    private Dictionary<Vector2I, bool> localRoadQueryCache = new(); // 小路查询缓存
    private int maxCacheSize = 10000;

    // 配置参数
    public int MergeThreshold { get; set; } = 32;
    public int DefaultRoadWidth { get; set; } = 3;
    public bool EnableRoadMerging { get; set; } = true; // 是否启用道路合并

    // 图论数据结构
    private RoadGraph roadGraph;

    /// <summary>
    /// 道路图节点
    /// </summary>
    public class GraphNode
    {
        public Vector2I Position { get; set; }
        public List<GraphEdge> Edges { get; set; } = new List<GraphEdge>();
        public int Id { get; set; }

        public GraphNode(Vector2I position, int id)
        {
            Position = position;
            Id = id;
        }
    }

    /// <summary>
    /// 道路图边
    /// </summary>
    public class GraphEdge
    {
        public GraphNode From { get; set; }
        public GraphNode To { get; set; }
        public List<RoadSegment> RoadSegments { get; set; } = new List<RoadSegment>();
        public float Weight { get; set; }

        public GraphEdge(GraphNode from, GraphNode to)
        {
            From = from;
            To = to;
            Weight = CalculateDistance(from.Position, to.Position);
        }

        private float CalculateDistance(Vector2I a, Vector2I b)
        {
            return Mathf.Sqrt((a.X - b.X) * (a.X - b.X) + (a.Y - b.Y) * (a.Y - b.Y));
        }
    }

    /// <summary>
    /// 道路图
    /// </summary>
    public class RoadGraph
    {
        public Dictionary<Vector2I, GraphNode> Nodes { get; set; } = new Dictionary<Vector2I, GraphNode>();
        public List<GraphEdge> Edges { get; set; } = new List<GraphEdge>();
        private int nextNodeId = 0;

        public GraphNode AddNode(Vector2I position)
        {
            if (Nodes.ContainsKey(position))
                return Nodes[position];

            var node = new GraphNode(position, nextNodeId++);
            Nodes[position] = node;
            return node;
        }

        public GraphEdge AddEdge(Vector2I from, Vector2I to)
        {
            var fromNode = AddNode(from);
            var toNode = AddNode(to);

            var edge = new GraphEdge(fromNode, toNode);
            fromNode.Edges.Add(edge);
            toNode.Edges.Add(edge);
            Edges.Add(edge);

            return edge;
        }

        public void Clear()
        {
            Nodes.Clear();
            Edges.Clear();
            nextNodeId = 0;
        }
    }

    /// <summary>
    /// 道路网格数据结构
    /// </summary>
    public class RoadGrid
    {
        public Dictionary<int, GridRoad> HorizontalRoads { get; set; } = new Dictionary<int, GridRoad>();
        public Dictionary<int, GridRoad> VerticalRoads { get; set; } = new Dictionary<int, GridRoad>();
        public HashSet<Vector2I> Intersections { get; set; } = new HashSet<Vector2I>();

        public void AddHorizontalRoad(int y, int startX, int endX)
        {
            if (!HorizontalRoads.ContainsKey(y))
                HorizontalRoads[y] = new GridRoad(y, true);
            HorizontalRoads[y].AddSegment(startX, endX);
        }

        public void AddVerticalRoad(int x, int startY, int endY)
        {
            if (!VerticalRoads.ContainsKey(x))
                VerticalRoads[x] = new GridRoad(x, false);
            VerticalRoads[x].AddSegment(startY, endY);
        }
    }

    /// <summary>
    /// 网格道路
    /// </summary>
    public class GridRoad
    {
        public int FixedCoordinate { get; set; }
        public bool IsHorizontal { get; set; }
        public List<(int start, int end)> Segments { get; set; } = new List<(int start, int end)>();

        public GridRoad(int fixedCoord, bool isHorizontal)
        {
            FixedCoordinate = fixedCoord;
            IsHorizontal = isHorizontal;
        }

        public void AddSegment(int start, int end)
        {
            if (start > end)
            {
                (start, end) = (end, start);
            }
            Segments.Add((start, end));
        }

        public void OptimizeSegments()
        {
            if (Segments.Count <= 1) return;

            // 排序并合并重叠的段
            Segments.Sort();
            var merged = new List<(int start, int end)>();
            var current = Segments[0];

            for (int i = 1; i < Segments.Count; i++)
            {
                var next = Segments[i];
                if (current.end >= next.start - 1) // 允许1个单位的间隙
                {
                    current = (current.start, Math.Max(current.end, next.end));
                }
                else
                {
                    merged.Add(current);
                    current = next;
                }
            }
            merged.Add(current);

            Segments = merged;
        }
    }

    /// <summary>
    /// 从ValidEdges生成优化的道路系统
    /// </summary>
    public void GenerateOptimizedRoadSystem(List<delaunay_algorithm.Edge> validEdges)
    {
        if (validEdges == null || validEdges.Count == 0)
        {
            GD.Print("没有有效边数据");
            return;
        }

        GD.Print($"🚀 开始生成道路系统（基于图论拓扑算法），共 {validEdges.Count} 条边");

        // 清空现有数据
        mainRoadSegments.Clear();
        localRoadSegments.Clear();
        allRoadSegments.Clear();
        mainRoadSpatialIndex.Clear();
        localRoadSpatialIndex.Clear();
        spatialIndex.Clear();
        queryCache.Clear();
        mainRoadQueryCache.Clear();
        localRoadQueryCache.Clear();

        // 初始化道路图
        roadGraph = new RoadGraph();

        // 基于图论的道路生成流程
        GenerateRoadsWithTopology(validEdges);

        GD.Print($"✅ 道路系统生成完成，共 {allRoadSegments.Count} 条道路段 (主路: {mainRoadSegments.Count}, 小路: {localRoadSegments.Count})");
    }

    /// <summary>
    /// 基于交点的道路生成算法
    /// </summary>
    private void GenerateRoadsWithTopology(List<delaunay_algorithm.Edge> validEdges)
    {
        GD.Print("🔍 第1步：分析连接需求");

        // 1. 提取所有端点
        var endPoints = ExtractEndPoints(validEdges);
        GD.Print($"   发现 {endPoints.Count} 个端点");

        GD.Print("📍 第2步：寻找道路交点");

        // 2. 寻找所有可能的道路交点
        var intersections = FindRoadIntersections(validEdges, endPoints);
        GD.Print($"   发现 {intersections.Count} 个道路交点");

        GD.Print("🛣️ 第3步：构建道路网格");

        // 3. 基于交点构建道路网格
        var roadGrid = BuildRoadGrid(intersections, endPoints);
        GD.Print($"   构建了 {roadGrid.HorizontalRoads.Count} 条水平道路，{roadGrid.VerticalRoads.Count} 条垂直道路");

        GD.Print("🔗 第4步：连接端点到网格");

        // 4. 将端点连接到道路网格
        ConnectEndPointsToGrid(validEdges, roadGrid);
        GD.Print($"   连接完成");

        GD.Print("🔧 第5步：合并相近道路");

        // 5. 合并距离较近的平行道路
        MergeNearbyRoads(roadGrid);
        GD.Print($"   相近道路合并完成");

        GD.Print("📊 第6步：生成最终道路段");

        // 6. 从道路网格生成最终道路段
        GenerateFinalRoadSegments(roadGrid);

        GD.Print("✂️ 第7步：处理断头路");

        // 7. 处理断头路（端点不在中心点的线段）
        TrimDeadEndRoads(endPoints);
        GD.Print($"   断头路处理完成");

        GD.Print("🔍 第8步：验证连通性");

        // 8. 验证所有连接
        ValidateConnections(validEdges);
    }

    /// <summary>
    /// 提取所有端点
    /// </summary>
    private HashSet<Vector2I> ExtractEndPoints(List<delaunay_algorithm.Edge> validEdges)
    {
        var endPoints = new HashSet<Vector2I>();

        foreach (var edge in validEdges)
        {
            endPoints.Add(new Vector2I((int)edge.a.x, (int)edge.a.y));
            endPoints.Add(new Vector2I((int)edge.b.x, (int)edge.b.y));
        }

        return endPoints;
    }

    /// <summary>
    /// 寻找道路交点
    /// </summary>
    private HashSet<Vector2I> FindRoadIntersections(List<delaunay_algorithm.Edge> validEdges, HashSet<Vector2I> endPoints)
    {
        var intersections = new HashSet<Vector2I>();

        // 为每条边生成L型路径，找出所有可能的交点
        var allPaths = new List<(Vector2I start, Vector2I end, List<Vector2I> path)>();

        foreach (var edge in validEdges)
        {
            Vector2I start = new Vector2I((int)edge.a.x, (int)edge.a.y);
            Vector2I end = new Vector2I((int)edge.b.x, (int)edge.b.y);

            // 生成L型路径的关键点
            var path = GenerateLShapePath(start, end);
            allPaths.Add((start, end, path));
        }

        // 找出路径之间的交点
        for (int i = 0; i < allPaths.Count; i++)
        {
            for (int j = i + 1; j < allPaths.Count; j++)
            {
                var pathIntersections = FindPathIntersections(allPaths[i].path, allPaths[j].path);
                foreach (var intersection in pathIntersections)
                {
                    intersections.Add(intersection);
                }
            }
        }

        // 添加所有端点作为交点
        foreach (var point in endPoints)
        {
            intersections.Add(point);
        }

        GD.Print($"   路径分析: {allPaths.Count} 条路径");
        GD.Print($"   交点详情: {intersections.Count} 个交点");

        return intersections;
    }

    /// <summary>
    /// 生成L型路径的关键点
    /// </summary>
    private List<Vector2I> GenerateLShapePath(Vector2I start, Vector2I end)
    {
        var path = new List<Vector2I>();

        path.Add(start);

        // L型路径的转折点
        if (start.X != end.X && start.Y != end.Y)
        {
            Vector2I corner = new Vector2I(end.X, start.Y);
            path.Add(corner);
        }

        path.Add(end);

        return path;
    }

    /// <summary>
    /// 找出两条路径的交点
    /// </summary>
    private List<Vector2I> FindPathIntersections(List<Vector2I> path1, List<Vector2I> path2)
    {
        var intersections = new List<Vector2I>();

        // 检查每条路径段与另一条路径的交点
        for (int i = 0; i < path1.Count - 1; i++)
        {
            for (int j = 0; j < path2.Count - 1; j++)
            {
                var intersection = FindSegmentIntersection(path1[i], path1[i + 1], path2[j], path2[j + 1]);
                if (intersection.HasValue)
                {
                    intersections.Add(intersection.Value);
                }
            }
        }

        return intersections;
    }

    /// <summary>
    /// 找出两个线段的交点（仅限水平和垂直线段）
    /// </summary>
    private Vector2I? FindSegmentIntersection(Vector2I seg1Start, Vector2I seg1End, Vector2I seg2Start, Vector2I seg2End)
    {
        // 确保线段是水平或垂直的
        bool seg1Horizontal = seg1Start.Y == seg1End.Y;
        bool seg1Vertical = seg1Start.X == seg1End.X;
        bool seg2Horizontal = seg2Start.Y == seg2End.Y;
        bool seg2Vertical = seg2Start.X == seg2End.X;

        // 只处理一条水平线和一条垂直线的交点
        if (seg1Horizontal && seg2Vertical)
        {
            return FindHorizontalVerticalIntersection(seg1Start, seg1End, seg2Start, seg2End);
        }
        else if (seg1Vertical && seg2Horizontal)
        {
            return FindHorizontalVerticalIntersection(seg2Start, seg2End, seg1Start, seg1End);
        }

        return null;
    }

    /// <summary>
    /// 找出水平线和垂直线的交点
    /// </summary>
    private Vector2I? FindHorizontalVerticalIntersection(Vector2I hStart, Vector2I hEnd, Vector2I vStart, Vector2I vEnd)
    {
        // 水平线的Y坐标
        int hY = hStart.Y;
        // 垂直线的X坐标
        int vX = vStart.X;

        // 检查交点是否在两条线段的范围内
        int hMinX = Math.Min(hStart.X, hEnd.X);
        int hMaxX = Math.Max(hStart.X, hEnd.X);
        int vMinY = Math.Min(vStart.Y, vEnd.Y);
        int vMaxY = Math.Max(vStart.Y, vEnd.Y);

        if (vX >= hMinX && vX <= hMaxX && hY >= vMinY && hY <= vMaxY)
        {
            return new Vector2I(vX, hY);
        }

        return null;
    }

    /// <summary>
    /// 构建道路网格
    /// </summary>
    private RoadGrid BuildRoadGrid(HashSet<Vector2I> intersections, HashSet<Vector2I> endPoints)
    {
        var roadGrid = new RoadGrid();
        roadGrid.Intersections = intersections;

        // 按X和Y坐标分组交点
        var xGroups = intersections.GroupBy(p => p.X).ToDictionary(g => g.Key, g => g.OrderBy(p => p.Y).ToList());
        var yGroups = intersections.GroupBy(p => p.Y).ToDictionary(g => g.Key, g => g.OrderBy(p => p.X).ToList());

        // 构建垂直道路（相同X坐标的点之间）
        foreach (var xGroup in xGroups)
        {
            int x = xGroup.Key;
            var points = xGroup.Value;

            if (points.Count > 1)
            {
                for (int i = 0; i < points.Count - 1; i++)
                {
                    roadGrid.AddVerticalRoad(x, points[i].Y, points[i + 1].Y);
                }
            }
        }

        // 构建水平道路（相同Y坐标的点之间）
        foreach (var yGroup in yGroups)
        {
            int y = yGroup.Key;
            var points = yGroup.Value;

            if (points.Count > 1)
            {
                for (int i = 0; i < points.Count - 1; i++)
                {
                    roadGrid.AddHorizontalRoad(y, points[i].X, points[i + 1].X);
                }
            }
        }

        // 优化道路段
        foreach (var road in roadGrid.HorizontalRoads.Values)
        {
            road.OptimizeSegments();
        }
        foreach (var road in roadGrid.VerticalRoads.Values)
        {
            road.OptimizeSegments();
        }

        GD.Print($"   网格构建详情:");
        GD.Print($"     水平道路线: {roadGrid.HorizontalRoads.Count} 条");
        GD.Print($"     垂直道路线: {roadGrid.VerticalRoads.Count} 条");

        return roadGrid;
    }

    /// <summary>
    /// 连接端点到道路网格
    /// </summary>
    private void ConnectEndPointsToGrid(List<delaunay_algorithm.Edge> validEdges, RoadGrid roadGrid)
    {
        foreach (var edge in validEdges)
        {
            Vector2I start = new Vector2I((int)edge.a.x, (int)edge.a.y);
            Vector2I end = new Vector2I((int)edge.b.x, (int)edge.b.y);

            // 确保起点和终点都连接到网格
            EnsurePointConnectedToGrid(start, roadGrid);
            EnsurePointConnectedToGrid(end, roadGrid);
        }
    }

    /// <summary>
    /// 确保点连接到道路网格
    /// </summary>
    private void EnsurePointConnectedToGrid(Vector2I point, RoadGrid roadGrid)
    {
        // 如果点已经是交点，则已经连接
        if (roadGrid.Intersections.Contains(point))
            return;

        // 寻找最近的水平和垂直道路
        var nearestHorizontal = FindNearestHorizontalRoad(point, roadGrid);
        var nearestVertical = FindNearestVerticalRoad(point, roadGrid);

        // 连接到最近的道路
        if (nearestHorizontal.HasValue)
        {
            int y = nearestHorizontal.Value;
            roadGrid.AddHorizontalRoad(y, Math.Min(point.X, point.X), point.X);
            roadGrid.AddVerticalRoad(point.X, Math.Min(point.Y, y), Math.Max(point.Y, y));
        }

        if (nearestVertical.HasValue)
        {
            int x = nearestVertical.Value;
            roadGrid.AddVerticalRoad(x, Math.Min(point.Y, point.Y), point.Y);
            roadGrid.AddHorizontalRoad(point.Y, Math.Min(point.X, x), Math.Max(point.X, x));
        }
    }

    /// <summary>
    /// 寻找最近的水平道路
    /// </summary>
    private int? FindNearestHorizontalRoad(Vector2I point, RoadGrid roadGrid)
    {
        int? nearest = null;
        int minDistance = int.MaxValue;

        foreach (var y in roadGrid.HorizontalRoads.Keys)
        {
            int distance = Math.Abs(point.Y - y);
            if (distance < minDistance)
            {
                minDistance = distance;
                nearest = y;
            }
        }

        return nearest;
    }

    /// <summary>
    /// 寻找最近的垂直道路
    /// </summary>
    private int? FindNearestVerticalRoad(Vector2I point, RoadGrid roadGrid)
    {
        int? nearest = null;
        int minDistance = int.MaxValue;

        foreach (var x in roadGrid.VerticalRoads.Keys)
        {
            int distance = Math.Abs(point.X - x);
            if (distance < minDistance)
            {
                minDistance = distance;
                nearest = x;
            }
        }

        return nearest;
    }

    /// <summary>
    /// 合并相近的道路
    /// </summary>
    private void MergeNearbyRoads(RoadGrid roadGrid)
    {
        GD.Print($"   开始合并相近道路，阈值: {MergeThreshold}");

        // 合并相近的水平道路
        int horizontalMerged = MergeNearbyHorizontalRoadsFixed(roadGrid);

        // 合并相近的垂直道路
        int verticalMerged = MergeNearbyVerticalRoadsFixed(roadGrid);

        GD.Print($"   合并结果: 水平道路合并 {horizontalMerged} 组，垂直道路合并 {verticalMerged} 组");
    }

    /// <summary>
    /// 合并相近的水平道路
    /// </summary>
    private int MergeNearbyHorizontalRoads(RoadGrid roadGrid)
    {
        var horizontalRoads = roadGrid.HorizontalRoads.ToList();
        int mergedCount = 0;
        bool hasChanges = true;

        while (hasChanges && mergedCount < 10) // 防止无限循环
        {
            hasChanges = false;
            var toRemove = new List<int>();
            var toAdd = new List<(int y, GridRoad road)>();

            for (int i = 0; i < horizontalRoads.Count; i++)
            {
                for (int j = i + 1; j < horizontalRoads.Count; j++)
                {
                    var road1 = horizontalRoads[i];
                    var road2 = horizontalRoads[j];

                    int distance = Math.Abs(road1.Key - road2.Key);

                    if (distance <= MergeThreshold && CanMergeHorizontalRoads(road1.Value, road2.Value, roadGrid))
                    {
                        // 合并这两条道路
                        var mergedRoad = MergeHorizontalRoadPair(road1.Value, road2.Value, roadGrid);
                        int newY = (road1.Key + road2.Key) / 2; // 使用中间位置

                        toRemove.Add(road1.Key);
                        toRemove.Add(road2.Key);
                        toAdd.Add((newY, mergedRoad));

                        GD.Print($"     合并水平道路: Y={road1.Key} 和 Y={road2.Key} -> Y={newY} (距离={distance})");

                        hasChanges = true;
                        mergedCount++;
                        break;
                    }
                }
                if (hasChanges) break;
            }

            // 应用更改
            foreach (var key in toRemove)
            {
                roadGrid.HorizontalRoads.Remove(key);
                horizontalRoads.RemoveAll(kvp => kvp.Key == key);
            }

            foreach (var (y, road) in toAdd)
            {
                roadGrid.HorizontalRoads[y] = road;
                horizontalRoads.Add(new KeyValuePair<int, GridRoad>(y, road));
            }
        }

        return mergedCount;
    }

    /// <summary>
    /// 合并相近的垂直道路
    /// </summary>
    private int MergeNearbyVerticalRoads(RoadGrid roadGrid)
    {
        var verticalRoads = roadGrid.VerticalRoads.ToList();
        int mergedCount = 0;
        bool hasChanges = true;

        while (hasChanges && mergedCount < 10) // 防止无限循环
        {
            hasChanges = false;
            var toRemove = new List<int>();
            var toAdd = new List<(int x, GridRoad road)>();

            for (int i = 0; i < verticalRoads.Count; i++)
            {
                for (int j = i + 1; j < verticalRoads.Count; j++)
                {
                    var road1 = verticalRoads[i];
                    var road2 = verticalRoads[j];

                    int distance = Math.Abs(road1.Key - road2.Key);

                    if (distance <= MergeThreshold && CanMergeVerticalRoads(road1.Value, road2.Value, roadGrid))
                    {
                        // 合并这两条道路
                        var mergedRoad = MergeVerticalRoadPair(road1.Value, road2.Value, roadGrid);
                        int newX = (road1.Key + road2.Key) / 2; // 使用中间位置

                        toRemove.Add(road1.Key);
                        toRemove.Add(road2.Key);
                        toAdd.Add((newX, mergedRoad));

                        GD.Print($"     合并垂直道路: X={road1.Key} 和 X={road2.Key} -> X={newX} (距离={distance})");

                        hasChanges = true;
                        mergedCount++;
                        break;
                    }
                }
                if (hasChanges) break;
            }

            // 应用更改
            foreach (var key in toRemove)
            {
                roadGrid.VerticalRoads.Remove(key);
                verticalRoads.RemoveAll(kvp => kvp.Key == key);
            }

            foreach (var (x, road) in toAdd)
            {
                roadGrid.VerticalRoads[x] = road;
                verticalRoads.Add(new KeyValuePair<int, GridRoad>(x, road));
            }
        }

        return mergedCount;
    }

    /// <summary>
    /// 检查是否可以合并两条水平道路
    /// </summary>
    private bool CanMergeHorizontalRoads(GridRoad road1, GridRoad road2, RoadGrid roadGrid)
    {
        // 检查两条道路是否有重叠的X范围
        var road1Range = GetRoadXRange(road1);
        var road2Range = GetRoadXRange(road2);

        // 如果没有重叠，不能合并
        if (road1Range.max < road2Range.min || road2Range.max < road1Range.min)
        {
            return false;
        }

        // 检查合并后是否会破坏交点
        int y1 = road1.FixedCoordinate;
        int y2 = road2.FixedCoordinate;
        int mergedY = (y1 + y2) / 2;

        // 检查在合并范围内是否有关键交点
        int minX = Math.Min(road1Range.min, road2Range.min);
        int maxX = Math.Max(road1Range.max, road2Range.max);

        foreach (var intersection in roadGrid.Intersections)
        {
            if ((intersection.Y == y1 || intersection.Y == y2) &&
                intersection.X >= minX && intersection.X <= maxX)
            {
                // 检查这个交点是否会因为合并而失去连接
                if (!WillIntersectionRemainConnected(intersection, mergedY, roadGrid))
                {
                    return false;
                }
            }
        }

        return true;
    }

    /// <summary>
    /// 检查是否可以合并两条垂直道路
    /// </summary>
    private bool CanMergeVerticalRoads(GridRoad road1, GridRoad road2, RoadGrid roadGrid)
    {
        // 检查两条道路是否有重叠的Y范围
        var road1Range = GetRoadYRange(road1);
        var road2Range = GetRoadYRange(road2);

        // 如果没有重叠，不能合并
        if (road1Range.max < road2Range.min || road2Range.max < road1Range.min)
        {
            return false;
        }

        // 检查合并后是否会破坏交点
        int x1 = road1.FixedCoordinate;
        int x2 = road2.FixedCoordinate;
        int mergedX = (x1 + x2) / 2;

        // 检查在合并范围内是否有关键交点
        int minY = Math.Min(road1Range.min, road2Range.min);
        int maxY = Math.Max(road1Range.max, road2Range.max);

        foreach (var intersection in roadGrid.Intersections)
        {
            if ((intersection.X == x1 || intersection.X == x2) &&
                intersection.Y >= minY && intersection.Y <= maxY)
            {
                // 检查这个交点是否会因为合并而失去连接
                if (!WillIntersectionRemainConnected(intersection, mergedX, roadGrid))
                {
                    return false;
                }
            }
        }

        return true;
    }

    /// <summary>
    /// 获取道路的X范围
    /// </summary>
    private (int min, int max) GetRoadXRange(GridRoad road)
    {
        if (road.Segments.Count == 0) return (0, 0);

        int min = road.Segments.Min(s => s.start);
        int max = road.Segments.Max(s => s.end);
        return (min, max);
    }

    /// <summary>
    /// 获取道路的Y范围
    /// </summary>
    private (int min, int max) GetRoadYRange(GridRoad road)
    {
        if (road.Segments.Count == 0) return (0, 0);

        int min = road.Segments.Min(s => s.start);
        int max = road.Segments.Max(s => s.end);
        return (min, max);
    }

    /// <summary>
    /// 检查交点在合并后是否仍然连通
    /// </summary>
    private bool WillIntersectionRemainConnected(Vector2I intersection, int newCoordinate, RoadGrid roadGrid)
    {
        // 简化版检查：如果交点附近有其他道路，则认为仍然连通
        int tolerance = MergeThreshold;

        // 检查是否有其他水平道路在附近
        foreach (var y in roadGrid.HorizontalRoads.Keys)
        {
            if (Math.Abs(y - newCoordinate) <= tolerance)
            {
                return true;
            }
        }

        // 检查是否有其他垂直道路在附近
        foreach (var x in roadGrid.VerticalRoads.Keys)
        {
            if (Math.Abs(x - newCoordinate) <= tolerance)
            {
                return true;
            }
        }

        return true; // 保守策略：默认允许合并
    }

    /// <summary>
    /// 合并两条水平道路
    /// </summary>
    private GridRoad MergeHorizontalRoadPair(GridRoad road1, GridRoad road2, RoadGrid roadGrid)
    {
        int newY = (road1.FixedCoordinate + road2.FixedCoordinate) / 2;
        var mergedRoad = new GridRoad(newY, true);

        // 合并所有段
        var allSegments = new List<(int start, int end)>();
        allSegments.AddRange(road1.Segments);
        allSegments.AddRange(road2.Segments);

        foreach (var segment in allSegments)
        {
            mergedRoad.AddSegment(segment.start, segment.end);
        }

        // 优化合并后的段
        mergedRoad.OptimizeSegments();

        return mergedRoad;
    }

    /// <summary>
    /// 合并两条垂直道路
    /// </summary>
    private GridRoad MergeVerticalRoadPair(GridRoad road1, GridRoad road2, RoadGrid roadGrid)
    {
        int newX = (road1.FixedCoordinate + road2.FixedCoordinate) / 2;
        var mergedRoad = new GridRoad(newX, false);

        // 合并所有段
        var allSegments = new List<(int start, int end)>();
        allSegments.AddRange(road1.Segments);
        allSegments.AddRange(road2.Segments);

        foreach (var segment in allSegments)
        {
            mergedRoad.AddSegment(segment.start, segment.end);
        }

        // 优化合并后的段
        mergedRoad.OptimizeSegments();

        return mergedRoad;
    }

    /// <summary>
    /// 修复版：合并相近的水平道路（保持连通性）
    /// </summary>
    private int MergeNearbyHorizontalRoadsFixed(RoadGrid roadGrid)
    {
        var horizontalRoads = roadGrid.HorizontalRoads.ToList();
        int mergedCount = 0;
        bool hasChanges = true;

        while (hasChanges && mergedCount < 10)
        {
            hasChanges = false;
            var toRemove = new List<int>();
            var toAdd = new List<(int y, GridRoad road)>();

            for (int i = 0; i < horizontalRoads.Count; i++)
            {
                for (int j = i + 1; j < horizontalRoads.Count; j++)
                {
                    var road1 = horizontalRoads[i];
                    var road2 = horizontalRoads[j];

                    int distance = Math.Abs(road1.Key - road2.Key);

                    if (distance <= MergeThreshold && CanSafelyMergeHorizontalRoads(road1.Value, road2.Value, roadGrid))
                    {
                        // 安全合并：不改变坐标，而是扩展道路覆盖范围
                        var mergedRoad = SafeMergeHorizontalRoads(road1.Value, road2.Value, roadGrid);
                        int targetY = ChooseBestYCoordinate(road1.Key, road2.Key, roadGrid);

                        toRemove.Add(road1.Key);
                        toRemove.Add(road2.Key);
                        toAdd.Add((targetY, mergedRoad));

                        GD.Print($"     安全合并水平道路: Y={road1.Key} 和 Y={road2.Key} -> Y={targetY} (距离={distance})");

                        hasChanges = true;
                        mergedCount++;
                        break;
                    }
                }
                if (hasChanges) break;
            }

            // 应用更改
            foreach (var key in toRemove)
            {
                roadGrid.HorizontalRoads.Remove(key);
                horizontalRoads.RemoveAll(kvp => kvp.Key == key);
            }

            foreach (var (y, road) in toAdd)
            {
                roadGrid.HorizontalRoads[y] = road;
                horizontalRoads.Add(new KeyValuePair<int, GridRoad>(y, road));
            }
        }

        return mergedCount;
    }

    /// <summary>
    /// 修复版：合并相近的垂直道路（保持连通性）
    /// </summary>
    private int MergeNearbyVerticalRoadsFixed(RoadGrid roadGrid)
    {
        var verticalRoads = roadGrid.VerticalRoads.ToList();
        int mergedCount = 0;
        bool hasChanges = true;

        while (hasChanges && mergedCount < 10)
        {
            hasChanges = false;
            var toRemove = new List<int>();
            var toAdd = new List<(int x, GridRoad road)>();

            for (int i = 0; i < verticalRoads.Count; i++)
            {
                for (int j = i + 1; j < verticalRoads.Count; j++)
                {
                    var road1 = verticalRoads[i];
                    var road2 = verticalRoads[j];

                    int distance = Math.Abs(road1.Key - road2.Key);

                    if (distance <= MergeThreshold && CanSafelyMergeVerticalRoads(road1.Value, road2.Value, roadGrid))
                    {
                        // 安全合并：不改变坐标，而是扩展道路覆盖范围
                        var mergedRoad = SafeMergeVerticalRoads(road1.Value, road2.Value, roadGrid);
                        int targetX = ChooseBestXCoordinate(road1.Key, road2.Key, roadGrid);

                        toRemove.Add(road1.Key);
                        toRemove.Add(road2.Key);
                        toAdd.Add((targetX, mergedRoad));

                        GD.Print($"     安全合并垂直道路: X={road1.Key} 和 X={road2.Key} -> X={targetX} (距离={distance})");

                        hasChanges = true;
                        mergedCount++;
                        break;
                    }
                }
                if (hasChanges) break;
            }

            // 应用更改
            foreach (var key in toRemove)
            {
                roadGrid.VerticalRoads.Remove(key);
                verticalRoads.RemoveAll(kvp => kvp.Key == key);
            }

            foreach (var (x, road) in toAdd)
            {
                roadGrid.VerticalRoads[x] = road;
                verticalRoads.Add(new KeyValuePair<int, GridRoad>(x, road));
            }
        }

        return mergedCount;
    }

    /// <summary>
    /// 检查是否可以安全合并水平道路
    /// </summary>
    private bool CanSafelyMergeHorizontalRoads(GridRoad road1, GridRoad road2, RoadGrid roadGrid)
    {
        // 检查两条道路是否有重叠的X范围
        var road1Range = GetRoadXRange(road1);
        var road2Range = GetRoadXRange(road2);

        // 必须有重叠才能合并
        return road1Range.max >= road2Range.min && road2Range.max >= road1Range.min;
    }

    /// <summary>
    /// 检查是否可以安全合并垂直道路
    /// </summary>
    private bool CanSafelyMergeVerticalRoads(GridRoad road1, GridRoad road2, RoadGrid roadGrid)
    {
        // 检查两条道路是否有重叠的Y范围
        var road1Range = GetRoadYRange(road1);
        var road2Range = GetRoadYRange(road2);

        // 必须有重叠才能合并
        return road1Range.max >= road2Range.min && road2Range.max >= road1Range.min;
    }

    /// <summary>
    /// 安全合并水平道路
    /// </summary>
    private GridRoad SafeMergeHorizontalRoads(GridRoad road1, GridRoad road2, RoadGrid roadGrid)
    {
        // 选择一个合适的Y坐标
        int targetY = ChooseBestYCoordinate(road1.FixedCoordinate, road2.FixedCoordinate, roadGrid);
        var mergedRoad = new GridRoad(targetY, true);

        // 合并所有段，并扩展覆盖范围
        var allSegments = new List<(int start, int end)>();
        allSegments.AddRange(road1.Segments);
        allSegments.AddRange(road2.Segments);

        // 添加连接段，确保两条道路之间的区域也被覆盖
        var road1Range = GetRoadXRange(road1);
        var road2Range = GetRoadXRange(road2);
        int minX = Math.Min(road1Range.min, road2Range.min);
        int maxX = Math.Max(road1Range.max, road2Range.max);
        allSegments.Add((minX, maxX));

        foreach (var segment in allSegments)
        {
            mergedRoad.AddSegment(segment.start, segment.end);
        }

        mergedRoad.OptimizeSegments();
        return mergedRoad;
    }

    /// <summary>
    /// 安全合并垂直道路
    /// </summary>
    private GridRoad SafeMergeVerticalRoads(GridRoad road1, GridRoad road2, RoadGrid roadGrid)
    {
        // 选择一个合适的X坐标
        int targetX = ChooseBestXCoordinate(road1.FixedCoordinate, road2.FixedCoordinate, roadGrid);
        var mergedRoad = new GridRoad(targetX, false);

        // 合并所有段，并扩展覆盖范围
        var allSegments = new List<(int start, int end)>();
        allSegments.AddRange(road1.Segments);
        allSegments.AddRange(road2.Segments);

        // 添加连接段，确保两条道路之间的区域也被覆盖
        var road1Range = GetRoadYRange(road1);
        var road2Range = GetRoadYRange(road2);
        int minY = Math.Min(road1Range.min, road2Range.min);
        int maxY = Math.Max(road1Range.max, road2Range.max);
        allSegments.Add((minY, maxY));

        foreach (var segment in allSegments)
        {
            mergedRoad.AddSegment(segment.start, segment.end);
        }

        mergedRoad.OptimizeSegments();
        return mergedRoad;
    }

    /// <summary>
    /// 选择最佳的Y坐标
    /// </summary>
    private int ChooseBestYCoordinate(int y1, int y2, RoadGrid roadGrid)
    {
        // 检查哪个Y坐标有更多的交点
        int intersections1 = CountIntersectionsAtY(y1, roadGrid);
        int intersections2 = CountIntersectionsAtY(y2, roadGrid);

        if (intersections1 > intersections2)
            return y1;
        else if (intersections2 > intersections1)
            return y2;
        else
            return (y1 + y2) / 2; // 如果相等，使用中间值
    }

    /// <summary>
    /// 选择最佳的X坐标
    /// </summary>
    private int ChooseBestXCoordinate(int x1, int x2, RoadGrid roadGrid)
    {
        // 检查哪个X坐标有更多的交点
        int intersections1 = CountIntersectionsAtX(x1, roadGrid);
        int intersections2 = CountIntersectionsAtX(x2, roadGrid);

        if (intersections1 > intersections2)
            return x1;
        else if (intersections2 > intersections1)
            return x2;
        else
            return (x1 + x2) / 2; // 如果相等，使用中间值
    }

    /// <summary>
    /// 计算指定Y坐标上的交点数量
    /// </summary>
    private int CountIntersectionsAtY(int y, RoadGrid roadGrid)
    {
        return roadGrid.Intersections.Count(p => p.Y == y);
    }

    /// <summary>
    /// 计算指定X坐标上的交点数量
    /// </summary>
    private int CountIntersectionsAtX(int x, RoadGrid roadGrid)
    {
        return roadGrid.Intersections.Count(p => p.X == x);
    }







    /// <summary>
    /// 从道路网格生成最终道路段
    /// </summary>
    private void GenerateFinalRoadSegments(RoadGrid roadGrid)
    {
        // 生成水平道路段
        foreach (var road in roadGrid.HorizontalRoads.Values)
        {
            foreach (var segment in road.Segments)
            {
                var roadSegment = new RoadSegment
                {
                    StartPoint = new Vector2I(segment.start, road.FixedCoordinate),
                    EndPoint = new Vector2I(segment.end, road.FixedCoordinate),
                    Width = DefaultRoadWidth,
                    Type = RoadType.MainRoad
                };

                roadSegment.CalculateBounds();

                // 添加到主路存储
                mainRoadSegments.Add(roadSegment);
                mainRoadSpatialIndex.AddRoadSegment(roadSegment);

                // 同时添加到统一存储（兼容性）
                allRoadSegments.Add(roadSegment);
                spatialIndex.AddRoadSegment(roadSegment);
            }
        }

        // 生成垂直道路段
        foreach (var road in roadGrid.VerticalRoads.Values)
        {
            foreach (var segment in road.Segments)
            {
                var roadSegment = new RoadSegment
                {
                    StartPoint = new Vector2I(road.FixedCoordinate, segment.start),
                    EndPoint = new Vector2I(road.FixedCoordinate, segment.end),
                    Width = DefaultRoadWidth,
                    Type = RoadType.MainRoad
                };

                roadSegment.CalculateBounds();

                // 添加到主路存储
                mainRoadSegments.Add(roadSegment);
                mainRoadSpatialIndex.AddRoadSegment(roadSegment);

                // 同时添加到统一存储（兼容性）
                allRoadSegments.Add(roadSegment);
                spatialIndex.AddRoadSegment(roadSegment);
            }
        }

        GD.Print($"   生成了 {allRoadSegments.Count} 个最终道路段");
    }

    /// <summary>
    /// 验证连接
    /// </summary>
    private void ValidateConnections(List<delaunay_algorithm.Edge> validEdges)
    {
        GD.Print("   验证端点连接...");

        int connectedCount = 0;
        int notConnectedCount = 0;

        foreach (var edge in validEdges)
        {
            Vector2I start = new Vector2I((int)edge.a.x, (int)edge.a.y);
            Vector2I end = new Vector2I((int)edge.b.x, (int)edge.b.y);

            bool startConnected = IsPointConnectedToRoadNetwork(start);
            bool endConnected = IsPointConnectedToRoadNetwork(end);
            bool pathExists = DoesPathExist(start, end);

            if (startConnected && endConnected && pathExists)
            {
                connectedCount++;
            }
            else
            {
                notConnectedCount++;
                GD.PrintErr($"     ❌ 连接失败: ({start.X}, {start.Y}) -> ({end.X}, {end.Y})");
                if (!startConnected) GD.PrintErr($"       起点未连接");
                if (!endConnected) GD.PrintErr($"       终点未连接");
                if (!pathExists) GD.PrintErr($"       路径不存在");
            }
        }

        if (notConnectedCount == 0)
        {
            GD.Print($"     ✅ 所有 {connectedCount} 条连接都成功");
        }
        else
        {
            GD.PrintErr($"     ⚠️ {notConnectedCount} 条连接失败，{connectedCount} 条成功");
        }

        // 打印最终统计
        PrintFinalStatistics();
    }

    /// <summary>
    /// 检查点是否连接到道路网络
    /// </summary>
    private bool IsPointConnectedToRoadNetwork(Vector2I point)
    {
        const int tolerance = 2;

        foreach (var road in allRoadSegments)
        {
            if (IsPointOnRoadSegment(point, road) || IsPointNearRoadSegment(point, road, tolerance))
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// 检查两点之间是否存在路径
    /// </summary>
    private bool DoesPathExist(Vector2I start, Vector2I end)
    {
        // 简化版路径检查：检查是否可以通过L型路径连接
        // 先水平到终点X坐标，再垂直到终点

        Vector2I intermediate = new Vector2I(end.X, start.Y);

        // 检查水平段
        bool horizontalExists = DoesHorizontalPathExist(start, intermediate);
        // 检查垂直段
        bool verticalExists = DoesVerticalPathExist(intermediate, end);

        return horizontalExists && verticalExists;
    }

    /// <summary>
    /// 检查水平路径是否存在
    /// </summary>
    private bool DoesHorizontalPathExist(Vector2I start, Vector2I end)
    {
        if (start.Y != end.Y) return false;
        if (start.X == end.X) return true;

        int y = start.Y;
        int minX = Math.Min(start.X, end.X);
        int maxX = Math.Max(start.X, end.X);

        // 检查是否有水平道路覆盖这个范围
        foreach (var road in allRoadSegments.Where(r => r.IsHorizontal))
        {
            if (road.StartPoint.Y == y)
            {
                int roadMinX = Math.Min(road.StartPoint.X, road.EndPoint.X);
                int roadMaxX = Math.Max(road.StartPoint.X, road.EndPoint.X);

                if (roadMinX <= minX && roadMaxX >= maxX)
                {
                    return true;
                }
            }
        }

        return false;
    }

    /// <summary>
    /// 检查垂直路径是否存在
    /// </summary>
    private bool DoesVerticalPathExist(Vector2I start, Vector2I end)
    {
        if (start.X != end.X) return false;
        if (start.Y == end.Y) return true;

        int x = start.X;
        int minY = Math.Min(start.Y, end.Y);
        int maxY = Math.Max(start.Y, end.Y);

        // 检查是否有垂直道路覆盖这个范围
        foreach (var road in allRoadSegments.Where(r => r.IsVertical))
        {
            if (road.StartPoint.X == x)
            {
                int roadMinY = Math.Min(road.StartPoint.Y, road.EndPoint.Y);
                int roadMaxY = Math.Max(road.StartPoint.Y, road.EndPoint.Y);

                if (roadMinY <= minY && roadMaxY >= maxY)
                {
                    return true;
                }
            }
        }

        return false;
    }

    /// <summary>
    /// 打印最终统计信息
    /// </summary>
    private void PrintFinalStatistics()
    {
        var horizontalRoads = allRoadSegments.Where(r => r.IsHorizontal).ToList();
        var verticalRoads = allRoadSegments.Where(r => r.IsVertical).ToList();

        GD.Print($"📊 最终统计:");
        GD.Print($"     水平道路段: {horizontalRoads.Count} 条");
        GD.Print($"     垂直道路段: {verticalRoads.Count} 条");
        GD.Print($"     总道路段: {allRoadSegments.Count} 条");

        // 计算总长度
        float totalLength = allRoadSegments.Sum(s =>
        {
            if (s.IsHorizontal)
                return Math.Abs(s.EndPoint.X - s.StartPoint.X);
            else if (s.IsVertical)
                return Math.Abs(s.EndPoint.Y - s.StartPoint.Y);
            return 0;
        });

        GD.Print($"     总长度: {totalLength:F0} 单位");
        GD.Print($"     平均段长: {(totalLength / allRoadSegments.Count):F1} 单位");
    }

    /// <summary>
    /// 构建拓扑图
    /// </summary>
    private void BuildTopologyGraph(List<delaunay_algorithm.Edge> validEdges)
    {
        foreach (var edge in validEdges)
        {
            Vector2I start = new Vector2I((int)edge.a.x, (int)edge.a.y);
            Vector2I end = new Vector2I((int)edge.b.x, (int)edge.b.y);

            // 在图中添加边（自动创建节点）
            roadGraph.AddEdge(start, end);
        }

        GD.Print($"   图构建详情：");
        GD.Print($"     节点数: {roadGraph.Nodes.Count}");
        GD.Print($"     边数: {roadGraph.Edges.Count}");

        // 分析节点度数
        AnalyzeNodeDegrees();
    }

    /// <summary>
    /// 分析节点度数
    /// </summary>
    private void AnalyzeNodeDegrees()
    {
        var degreeCount = new Dictionary<int, int>();

        foreach (var node in roadGraph.Nodes.Values)
        {
            int degree = node.Edges.Count;
            if (!degreeCount.ContainsKey(degree))
                degreeCount[degree] = 0;
            degreeCount[degree]++;
        }

        GD.Print($"     节点度数分布:");
        foreach (var kvp in degreeCount.OrderBy(x => x.Key))
        {
            GD.Print($"       度数{kvp.Key}: {kvp.Value}个节点");
        }
    }

    /// <summary>
    /// 为每条图边生成道路路径
    /// </summary>
    private void GeneratePathsForEdges()
    {
        foreach (var graphEdge in roadGraph.Edges)
        {
            // 为每条图边生成曼哈顿路径（L型）
            var roadSegments = GenerateManhattanPath(graphEdge.From.Position, graphEdge.To.Position);
            graphEdge.RoadSegments.AddRange(roadSegments);
        }
    }

    /// <summary>
    /// 生成曼哈顿路径（L型路径）
    /// </summary>
    private List<RoadSegment> GenerateManhattanPath(Vector2I start, Vector2I end)
    {
        var segments = new List<RoadSegment>();

        // L型路径：先水平，后垂直
        if (start.X != end.X)
        {
            // 水平段
            segments.Add(new RoadSegment
            {
                StartPoint = start,
                EndPoint = new Vector2I(end.X, start.Y),
                Width = DefaultRoadWidth,
                Type = RoadType.MainRoad
            });
        }

        if (start.Y != end.Y)
        {
            // 垂直段
            segments.Add(new RoadSegment
            {
                StartPoint = new Vector2I(end.X, start.Y),
                EndPoint = end,
                Width = DefaultRoadWidth,
                Type = RoadType.MainRoad
            });
        }

        return segments;
    }

    /// <summary>
    /// 基于拓扑结构优化道路
    /// </summary>
    private void OptimizeRoadTopology()
    {
        GD.Print("   开始拓扑优化...");

        // 1. 收集所有道路段
        var allSegments = new List<RoadSegment>();
        foreach (var graphEdge in roadGraph.Edges)
        {
            allSegments.AddRange(graphEdge.RoadSegments);
        }

        GD.Print($"     优化前道路段数: {allSegments.Count}");

        // 2. 基于拓扑结构进行优化
        var optimizedSegments = OptimizeWithTopology(allSegments);

        GD.Print($"     优化后道路段数: {optimizedSegments.Count}");

        // 3. 更新图边的道路段
        UpdateGraphEdgesWithOptimizedRoads(optimizedSegments);
    }

    /// <summary>
    /// 基于拓扑结构优化道路段
    /// </summary>
    private List<RoadSegment> OptimizeWithTopology(List<RoadSegment> segments)
    {
        // 1. 识别关键拓扑点（度数 > 2 的节点）
        var criticalPoints = IdentifyCriticalTopologyPoints();

        // 2. 按方向分组
        var horizontalRoads = segments.Where(s => s.IsHorizontal).ToList();
        var verticalRoads = segments.Where(s => s.IsVertical).ToList();

        var result = new List<RoadSegment>();

        // 3. 拓扑感知合并
        result.AddRange(TopologyAwareMerge(horizontalRoads, criticalPoints, true));
        result.AddRange(TopologyAwareMerge(verticalRoads, criticalPoints, false));

        return result;
    }

    /// <summary>
    /// 识别关键拓扑点
    /// </summary>
    private HashSet<Vector2I> IdentifyCriticalTopologyPoints()
    {
        var criticalPoints = new HashSet<Vector2I>();

        foreach (var node in roadGraph.Nodes.Values)
        {
            // 度数大于2的节点是关键交叉点
            if (node.Edges.Count > 2)
            {
                criticalPoints.Add(node.Position);
            }
            // 度数为1的节点是端点
            else if (node.Edges.Count == 1)
            {
                criticalPoints.Add(node.Position);
            }
        }

        GD.Print($"     识别出 {criticalPoints.Count} 个关键拓扑点");
        return criticalPoints;
    }

    /// <summary>
    /// 拓扑感知的道路合并
    /// </summary>
    private List<RoadSegment> TopologyAwareMerge(List<RoadSegment> roads, HashSet<Vector2I> criticalPoints, bool isHorizontal)
    {
        if (roads.Count <= 1) return roads;

        // 按坐标分组
        var groups = roads.GroupBy(r => isHorizontal ? r.StartPoint.Y : r.StartPoint.X);
        var result = new List<RoadSegment>();

        foreach (var group in groups)
        {
            var groupRoads = group.ToList();
            int fixedCoord = group.Key;

            if (groupRoads.Count == 1)
            {
                result.AddRange(groupRoads);
                continue;
            }

            // 拓扑感知合并：检查是否会破坏拓扑结构
            if (CanMergeWithoutBreakingTopology(groupRoads, criticalPoints, isHorizontal))
            {
                var merged = CreateTopologyAwareMergedRoad(groupRoads, criticalPoints, isHorizontal);
                result.AddRange(merged);
                GD.Print($"     拓扑合并: {(isHorizontal ? "水平" : "垂直")}坐标{fixedCoord}, {groupRoads.Count}条 -> {merged.Count}条");
            }
            else
            {
                result.AddRange(groupRoads);
                GD.Print($"     拓扑保护: {(isHorizontal ? "水平" : "垂直")}坐标{fixedCoord}, {groupRoads.Count}条保持独立");
            }
        }

        return result;
    }

    /// <summary>
    /// 检查是否可以在不破坏拓扑的情况下合并
    /// </summary>
    private bool CanMergeWithoutBreakingTopology(List<RoadSegment> roads, HashSet<Vector2I> criticalPoints, bool isHorizontal)
    {
        // 获取合并范围
        int minVar = roads.Min(r => isHorizontal ?
            Math.Min(r.StartPoint.X, r.EndPoint.X) :
            Math.Min(r.StartPoint.Y, r.EndPoint.Y));
        int maxVar = roads.Max(r => isHorizontal ?
            Math.Max(r.StartPoint.X, r.EndPoint.X) :
            Math.Max(r.StartPoint.Y, r.EndPoint.Y));

        int fixedCoord = isHorizontal ? roads[0].StartPoint.Y : roads[0].StartPoint.X;

        // 检查合并范围内的关键拓扑点
        foreach (var point in criticalPoints)
        {
            int pointFixed = isHorizontal ? point.Y : point.X;
            int pointVar = isHorizontal ? point.X : point.Y;

            // 如果关键点在合并线上且在范围内
            if (pointFixed == fixedCoord && pointVar >= minVar && pointVar <= maxVar)
            {
                // 检查这个点是否是这组道路的端点
                bool isEndpoint = roads.Any(r => r.StartPoint == point || r.EndPoint == point);
                if (!isEndpoint)
                {
                    // 关键点在中间，不能简单合并
                    return false;
                }
            }
        }

        return true;
    }

    /// <summary>
    /// 创建拓扑感知的合并道路
    /// </summary>
    private List<RoadSegment> CreateTopologyAwareMergedRoad(List<RoadSegment> roads, HashSet<Vector2I> criticalPoints, bool isHorizontal)
    {
        // 找出所有关键分割点
        var splitPoints = new HashSet<int>();
        int fixedCoord = isHorizontal ? roads[0].StartPoint.Y : roads[0].StartPoint.X;

        // 添加关键拓扑点作为分割点
        foreach (var point in criticalPoints)
        {
            int pointFixed = isHorizontal ? point.Y : point.X;
            int pointVar = isHorizontal ? point.X : point.Y;

            if (pointFixed == fixedCoord)
            {
                splitPoints.Add(pointVar);
            }
        }

        // 添加道路端点
        foreach (var road in roads)
        {
            if (isHorizontal)
            {
                splitPoints.Add(road.StartPoint.X);
                splitPoints.Add(road.EndPoint.X);
            }
            else
            {
                splitPoints.Add(road.StartPoint.Y);
                splitPoints.Add(road.EndPoint.Y);
            }
        }

        var sortedSplitPoints = splitPoints.OrderBy(x => x).ToList();
        var result = new List<RoadSegment>();

        // 在分割点之间创建连续段
        for (int i = 0; i < sortedSplitPoints.Count - 1; i++)
        {
            int start = sortedSplitPoints[i];
            int end = sortedSplitPoints[i + 1];

            if (start == end) continue;

            // 检查这个区间是否有道路覆盖
            bool hasCoverage = roads.Any(road =>
            {
                int roadStart = isHorizontal ? Math.Min(road.StartPoint.X, road.EndPoint.X) : Math.Min(road.StartPoint.Y, road.EndPoint.Y);
                int roadEnd = isHorizontal ? Math.Max(road.StartPoint.X, road.EndPoint.X) : Math.Max(road.StartPoint.Y, road.EndPoint.Y);
                return roadStart <= start && roadEnd >= end;
            });

            if (hasCoverage)
            {
                var segment = new RoadSegment
                {
                    Width = roads.Max(r => r.Width),
                    Type = roads.First().Type
                };

                if (isHorizontal)
                {
                    segment.StartPoint = new Vector2I(start, fixedCoord);
                    segment.EndPoint = new Vector2I(end, fixedCoord);
                }
                else
                {
                    segment.StartPoint = new Vector2I(fixedCoord, start);
                    segment.EndPoint = new Vector2I(fixedCoord, end);
                }

                result.Add(segment);
            }
        }

        return result;
    }

    /// <summary>
    /// 更新图边的优化道路段
    /// </summary>
    private void UpdateGraphEdgesWithOptimizedRoads(List<RoadSegment> optimizedSegments)
    {
        // 清空所有图边的道路段
        foreach (var graphEdge in roadGraph.Edges)
        {
            graphEdge.RoadSegments.Clear();
        }

        // 将优化后的道路段重新分配给图边
        foreach (var segment in optimizedSegments)
        {
            // 找到最匹配的图边
            var matchingEdge = FindMatchingGraphEdge(segment);
            if (matchingEdge != null)
            {
                matchingEdge.RoadSegments.Add(segment);
            }
        }
    }

    /// <summary>
    /// 找到与道路段匹配的图边
    /// </summary>
    private GraphEdge FindMatchingGraphEdge(RoadSegment segment)
    {
        foreach (var graphEdge in roadGraph.Edges)
        {
            // 检查道路段是否在图边的路径上
            if (IsSegmentOnGraphEdgePath(segment, graphEdge))
            {
                return graphEdge;
            }
        }
        return null;
    }

    /// <summary>
    /// 检查道路段是否在图边路径上
    /// </summary>
    private bool IsSegmentOnGraphEdgePath(RoadSegment segment, GraphEdge graphEdge)
    {
        Vector2I start = graphEdge.From.Position;
        Vector2I end = graphEdge.To.Position;

        // 检查道路段是否是L型路径的一部分
        if (segment.IsHorizontal)
        {
            // 水平段应该在起点Y坐标或终点Y坐标上
            return (segment.StartPoint.Y == start.Y || segment.StartPoint.Y == end.Y) &&
                   IsSegmentBetweenPoints(segment, start, end, true);
        }
        else if (segment.IsVertical)
        {
            // 垂直段应该在起点X坐标或终点X坐标上
            return (segment.StartPoint.X == start.X || segment.StartPoint.X == end.X) &&
                   IsSegmentBetweenPoints(segment, start, end, false);
        }

        return false;
    }

    /// <summary>
    /// 检查道路段是否在两点之间
    /// </summary>
    private bool IsSegmentBetweenPoints(RoadSegment segment, Vector2I start, Vector2I end, bool checkHorizontal)
    {
        if (checkHorizontal)
        {
            int minX = Math.Min(start.X, end.X);
            int maxX = Math.Max(start.X, end.X);
            int segMinX = Math.Min(segment.StartPoint.X, segment.EndPoint.X);
            int segMaxX = Math.Max(segment.StartPoint.X, segment.EndPoint.X);

            return segMinX >= minX && segMaxX <= maxX;
        }
        else
        {
            int minY = Math.Min(start.Y, end.Y);
            int maxY = Math.Max(start.Y, end.Y);
            int segMinY = Math.Min(segment.StartPoint.Y, segment.EndPoint.Y);
            int segMaxY = Math.Max(segment.StartPoint.Y, segment.EndPoint.Y);

            return segMinY >= minY && segMaxY <= maxY;
        }
    }

    /// <summary>
    /// 从图构建空间索引
    /// </summary>
    private void BuildSpatialIndexFromGraph()
    {
        foreach (var graphEdge in roadGraph.Edges)
        {
            foreach (var segment in graphEdge.RoadSegments)
            {
                segment.CalculateBounds();
                allRoadSegments.Add(segment);
                spatialIndex.AddRoadSegment(segment);
            }
        }
    }

    /// <summary>
    /// 验证拓扑完整性
    /// </summary>
    private void ValidateTopology()
    {
        GD.Print("   验证拓扑完整性...");

        // 1. 验证节点连通性
        ValidateNodeConnectivity();

        // 2. 验证边完整性
        ValidateEdgeIntegrity();

        // 3. 验证道路连续性
        ValidateRoadContinuity();

        // 4. 统计拓扑信息
        PrintTopologyStatistics();
    }

    /// <summary>
    /// 验证节点连通性
    /// </summary>
    private void ValidateNodeConnectivity()
    {
        int connectedNodes = 0;
        int isolatedNodes = 0;

        foreach (var node in roadGraph.Nodes.Values)
        {
            bool hasRoadConnection = false;

            foreach (var edge in node.Edges)
            {
                if (edge.RoadSegments.Count > 0)
                {
                    hasRoadConnection = true;
                    break;
                }
            }

            if (hasRoadConnection)
            {
                connectedNodes++;
            }
            else
            {
                isolatedNodes++;
                GD.PrintErr($"     ❌ 孤立节点: ({node.Position.X}, {node.Position.Y})");
            }
        }

        if (isolatedNodes == 0)
        {
            GD.Print($"     ✅ 所有 {connectedNodes} 个节点都已连接");
        }
        else
        {
            GD.PrintErr($"     ⚠️ {isolatedNodes} 个孤立节点，{connectedNodes} 个已连接");
        }
    }

    /// <summary>
    /// 验证边完整性
    /// </summary>
    private void ValidateEdgeIntegrity()
    {
        int completeEdges = 0;
        int incompleteEdges = 0;

        foreach (var edge in roadGraph.Edges)
        {
            if (edge.RoadSegments.Count > 0)
            {
                completeEdges++;
            }
            else
            {
                incompleteEdges++;
                GD.PrintErr($"     ❌ 缺失道路的边: ({edge.From.Position.X}, {edge.From.Position.Y}) -> ({edge.To.Position.X}, {edge.To.Position.Y})");
            }
        }

        if (incompleteEdges == 0)
        {
            GD.Print($"     ✅ 所有 {completeEdges} 条边都有道路");
        }
        else
        {
            GD.PrintErr($"     ⚠️ {incompleteEdges} 条边缺失道路，{completeEdges} 条完整");
        }
    }

    /// <summary>
    /// 验证道路连续性
    /// </summary>
    private void ValidateRoadContinuity()
    {
        // 检查道路段之间的连续性
        int discontinuities = 0;

        foreach (var edge in roadGraph.Edges)
        {
            if (edge.RoadSegments.Count > 1)
            {
                for (int i = 0; i < edge.RoadSegments.Count - 1; i++)
                {
                    var current = edge.RoadSegments[i];
                    var next = edge.RoadSegments[i + 1];

                    if (!AreSegmentsConnected(current, next))
                    {
                        discontinuities++;
                        GD.PrintErr($"     ❌ 道路段不连续: {current.EndPoint} -> {next.StartPoint}");
                    }
                }
            }
        }

        if (discontinuities == 0)
        {
            GD.Print($"     ✅ 所有道路段都连续");
        }
        else
        {
            GD.PrintErr($"     ⚠️ 发现 {discontinuities} 处道路不连续");
        }
    }

    /// <summary>
    /// 检查两个道路段是否连接
    /// </summary>
    private bool AreSegmentsConnected(RoadSegment seg1, RoadSegment seg2)
    {
        return seg1.EndPoint == seg2.StartPoint || seg1.StartPoint == seg2.EndPoint ||
               seg1.EndPoint == seg2.EndPoint || seg1.StartPoint == seg2.StartPoint;
    }

    /// <summary>
    /// 打印拓扑统计信息
    /// </summary>
    private void PrintTopologyStatistics()
    {
        GD.Print($"📊 拓扑统计信息:");
        GD.Print($"     图节点数: {roadGraph.Nodes.Count}");
        GD.Print($"     图边数: {roadGraph.Edges.Count}");
        GD.Print($"     道路段数: {allRoadSegments.Count}");

        // 计算平均度数
        double avgDegree = roadGraph.Nodes.Values.Average(n => n.Edges.Count);
        GD.Print($"     平均节点度数: {avgDegree:F2}");

        // 计算道路总长度
        float totalLength = allRoadSegments.Sum(s =>
        {
            if (s.IsHorizontal)
                return Math.Abs(s.EndPoint.X - s.StartPoint.X);
            else if (s.IsVertical)
                return Math.Abs(s.EndPoint.Y - s.StartPoint.Y);
            return 0;
        });

        GD.Print($"     道路总长度: {totalLength:F0} 单位");
    }

    /// <summary>
    /// 全新的道路生成算法
    /// </summary>
    private void GenerateRoadsNewAlgorithm(List<delaunay_algorithm.Edge> validEdges)
    {
        GD.Print("📍 第1步：提取连接点");

        // 1. 提取所有需要连接的点
        var connectionPoints = ExtractConnectionPoints(validEdges);
        GD.Print($"   发现 {connectionPoints.Count} 个连接点");

        GD.Print("🛣️ 第2步：生成直连道路");

        // 2. 为每条边生成直连道路（L型路径）
        var directRoads = GenerateDirectRoads(validEdges);
        GD.Print($"   生成 {directRoads.Count} 条直连道路段");

        GD.Print("🔧 第3步：智能合并道路");

        // 3. 智能合并相近的平行道路
        var mergedRoads = SmartMergeRoads(directRoads, connectionPoints);
        GD.Print($"   合并后剩余 {mergedRoads.Count} 条道路段");

        GD.Print("📊 第4步：建立索引和验证");

        // 4. 建立空间索引
        foreach (var road in mergedRoads)
        {
            road.CalculateBounds();
            allRoadSegments.Add(road);
            spatialIndex.AddRoadSegment(road);
        }

        // 5. 验证结果
        ValidateRoadSystem(connectionPoints, validEdges);
    }

    /// <summary>
    /// 提取所有连接点
    /// </summary>
    private HashSet<Vector2I> ExtractConnectionPoints(List<delaunay_algorithm.Edge> validEdges)
    {
        var points = new HashSet<Vector2I>();

        foreach (var edge in validEdges)
        {
            points.Add(new Vector2I((int)edge.a.x, (int)edge.a.y));
            points.Add(new Vector2I((int)edge.b.x, (int)edge.b.y));
        }

        return points;
    }

    /// <summary>
    /// 生成直连道路（L型路径）
    /// </summary>
    private List<RoadSegment> GenerateDirectRoads(List<delaunay_algorithm.Edge> validEdges)
    {
        var roads = new List<RoadSegment>();

        foreach (var edge in validEdges)
        {
            Vector2I start = new Vector2I((int)edge.a.x, (int)edge.a.y);
            Vector2I end = new Vector2I((int)edge.b.x, (int)edge.b.y);

            // 生成L型路径：先水平，后垂直
            if (start.X != end.X)
            {
                // 水平段：从起点到 (终点X, 起点Y)
                roads.Add(new RoadSegment
                {
                    StartPoint = start,
                    EndPoint = new Vector2I(end.X, start.Y),
                    Width = DefaultRoadWidth,
                    Type = RoadType.MainRoad
                });
            }

            if (start.Y != end.Y)
            {
                // 垂直段：从 (终点X, 起点Y) 到终点
                roads.Add(new RoadSegment
                {
                    StartPoint = new Vector2I(end.X, start.Y),
                    EndPoint = end,
                    Width = DefaultRoadWidth,
                    Type = RoadType.MainRoad
                });
            }
        }

        return roads;
    }

    /// <summary>
    /// 智能合并道路
    /// </summary>
    private List<RoadSegment> SmartMergeRoads(List<RoadSegment> roads, HashSet<Vector2I> connectionPoints)
    {
        var horizontalRoads = roads.Where(r => r.IsHorizontal).ToList();
        var verticalRoads = roads.Where(r => r.IsVertical).ToList();

        var result = new List<RoadSegment>();

        // 合并水平道路
        result.AddRange(MergeRoadsByCoordinate(horizontalRoads, connectionPoints, true));

        // 合并垂直道路
        result.AddRange(MergeRoadsByCoordinate(verticalRoads, connectionPoints, false));

        return result;
    }

    /// <summary>
    /// 按坐标合并道路
    /// </summary>
    private List<RoadSegment> MergeRoadsByCoordinate(List<RoadSegment> roads, HashSet<Vector2I> connectionPoints, bool isHorizontal)
    {
        if (roads.Count <= 1) return roads;

        // 按固定坐标分组
        var groups = roads.GroupBy(r => isHorizontal ? r.StartPoint.Y : r.StartPoint.X);
        var result = new List<RoadSegment>();

        foreach (var group in groups)
        {
            var groupRoads = group.ToList();
            int fixedCoord = group.Key;

            if (groupRoads.Count == 1)
            {
                result.AddRange(groupRoads);
                continue;
            }

            // 检查这组道路是否可以合并
            if (CanMergeGroup(groupRoads, connectionPoints, isHorizontal))
            {
                var merged = MergeRoadGroup(groupRoads, isHorizontal);
                result.Add(merged);
                GD.Print($"   合并{(isHorizontal ? "水平" : "垂直")}道路组: 坐标{fixedCoord}, {groupRoads.Count}条 -> 1条");
            }
            else
            {
                result.AddRange(groupRoads);
                GD.Print($"   保持{(isHorizontal ? "水平" : "垂直")}道路组: 坐标{fixedCoord}, {groupRoads.Count}条 (有连接点冲突)");
            }
        }

        return result;
    }

    /// <summary>
    /// 检查道路组是否可以合并
    /// </summary>
    private bool CanMergeGroup(List<RoadSegment> roads, HashSet<Vector2I> connectionPoints, bool isHorizontal)
    {
        // 获取合并后的范围
        int minVar = roads.Min(r => isHorizontal ?
            Math.Min(r.StartPoint.X, r.EndPoint.X) :
            Math.Min(r.StartPoint.Y, r.EndPoint.Y));
        int maxVar = roads.Max(r => isHorizontal ?
            Math.Max(r.StartPoint.X, r.EndPoint.X) :
            Math.Max(r.StartPoint.Y, r.EndPoint.Y));

        int fixedCoord = isHorizontal ? roads[0].StartPoint.Y : roads[0].StartPoint.X;

        // 收集所有端点
        var allEndpoints = new HashSet<Vector2I>();
        foreach (var road in roads)
        {
            allEndpoints.Add(road.StartPoint);
            allEndpoints.Add(road.EndPoint);
        }

        // 检查在合并范围内是否有其他连接点
        foreach (var point in connectionPoints)
        {
            int pointFixed = isHorizontal ? point.Y : point.X;
            int pointVar = isHorizontal ? point.X : point.Y;

            // 如果连接点在同一条线上，且在合并范围内
            if (pointFixed == fixedCoord && pointVar >= minVar && pointVar <= maxVar)
            {
                // 但不是这组道路的端点，则不能合并
                if (!allEndpoints.Contains(point))
                {
                    return false;
                }
            }
        }

        return true;
    }

    /// <summary>
    /// 验证道路系统
    /// </summary>
    private void ValidateRoadSystem(HashSet<Vector2I> connectionPoints, List<delaunay_algorithm.Edge> originalEdges)
    {
        GD.Print("🔍 验证道路系统质量...");

        // 1. 检查连通性
        ValidateConnectivity(connectionPoints);

        // 2. 检查相近道路
        ValidateNearbyRoads();

        // 3. 统计信息
        PrintRoadStatistics();

        GD.Print("✅ 道路系统验证完成");
    }

    /// <summary>
    /// 验证连通性
    /// </summary>
    private void ValidateConnectivity(HashSet<Vector2I> connectionPoints)
    {
        int connectedCount = 0;
        int notConnectedCount = 0;

        foreach (var point in connectionPoints)
        {
            bool isConnected = IsPointConnectedToRoad(point);
            if (isConnected)
            {
                connectedCount++;
            }
            else
            {
                notConnectedCount++;
                GD.PrintErr($"   ❌ 连接点 ({point.X}, {point.Y}) 未连接到道路");
            }
        }

        if (notConnectedCount == 0)
        {
            GD.Print($"   ✅ 所有 {connectedCount} 个连接点都已正确连接");
        }
        else
        {
            GD.PrintErr($"   ⚠️ {notConnectedCount} 个连接点未连接，{connectedCount} 个已连接");
        }
    }

    /// <summary>
    /// 验证相近道路
    /// </summary>
    private void ValidateNearbyRoads()
    {
        var horizontalRoads = allRoadSegments.Where(r => r.IsHorizontal).ToList();
        var verticalRoads = allRoadSegments.Where(r => r.IsVertical).ToList();

        int nearbyCount = 0;

        // 检查水平道路
        for (int i = 0; i < horizontalRoads.Count; i++)
        {
            for (int j = i + 1; j < horizontalRoads.Count; j++)
            {
                int distance = Math.Abs(horizontalRoads[i].StartPoint.Y - horizontalRoads[j].StartPoint.Y);
                if (distance < MergeThreshold)
                {
                    nearbyCount++;
                    GD.PrintErr($"   ❌ 相近水平道路: Y={horizontalRoads[i].StartPoint.Y} 和 Y={horizontalRoads[j].StartPoint.Y}, 距离={distance}");
                }
            }
        }

        // 检查垂直道路
        for (int i = 0; i < verticalRoads.Count; i++)
        {
            for (int j = i + 1; j < verticalRoads.Count; j++)
            {
                int distance = Math.Abs(verticalRoads[i].StartPoint.X - verticalRoads[j].StartPoint.X);
                if (distance < MergeThreshold)
                {
                    nearbyCount++;
                    GD.PrintErr($"   ❌ 相近垂直道路: X={verticalRoads[i].StartPoint.X} 和 X={verticalRoads[j].StartPoint.X}, 距离={distance}");
                }
            }
        }

        if (nearbyCount == 0)
        {
            GD.Print("   ✅ 没有发现相近道路");
        }
        else
        {
            GD.PrintErr($"   ⚠️ 发现 {nearbyCount} 对相近道路");
        }
    }

    /// <summary>
    /// 检查点是否连接到道路
    /// </summary>
    private bool IsPointConnectedToRoad(Vector2I point)
    {
        const int tolerance = 2;

        foreach (var road in allRoadSegments)
        {
            if (IsPointOnRoadSegment(point, road) || IsPointNearRoadSegment(point, road, tolerance))
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// 打印道路统计信息
    /// </summary>
    private void PrintRoadStatistics()
    {
        var horizontalRoads = allRoadSegments.Where(r => r.IsHorizontal).ToList();
        var verticalRoads = allRoadSegments.Where(r => r.IsVertical).ToList();

        GD.Print($"📊 道路统计:");
        GD.Print($"   水平道路: {horizontalRoads.Count} 条");
        GD.Print($"   垂直道路: {verticalRoads.Count} 条");
        GD.Print($"   总计: {allRoadSegments.Count} 条道路段");

        // 计算总长度
        float totalLength = 0;
        foreach (var road in allRoadSegments)
        {
            if (road.IsHorizontal)
            {
                totalLength += Math.Abs(road.EndPoint.X - road.StartPoint.X);
            }
            else if (road.IsVertical)
            {
                totalLength += Math.Abs(road.EndPoint.Y - road.StartPoint.Y);
            }
        }

        GD.Print($"   总长度: {totalLength:F0} 单位");
    }

    /// <summary>
    /// 验证是否还有相近的道路
    /// </summary>
    private void ValidateNoNearbyRoads()
    {
        var horizontalRoads = allRoadSegments.Where(s => s.IsHorizontal).ToList();
        var verticalRoads = allRoadSegments.Where(s => s.IsVertical).ToList();

        // 检查水平道路
        for (int i = 0; i < horizontalRoads.Count; i++)
        {
            for (int j = i + 1; j < horizontalRoads.Count; j++)
            {
                int yDiff = Math.Abs(horizontalRoads[i].StartPoint.Y - horizontalRoads[j].StartPoint.Y);
                if (yDiff < MergeThreshold)
                {
                    GD.PrintErr($"❌ 发现相近的水平道路！Y坐标差距: {yDiff}, 阈值: {MergeThreshold}");
                    GD.PrintErr($"道路1: ({horizontalRoads[i].StartPoint.X}, {horizontalRoads[i].StartPoint.Y}) -> ({horizontalRoads[i].EndPoint.X}, {horizontalRoads[i].EndPoint.Y})");
                    GD.PrintErr($"道路2: ({horizontalRoads[j].StartPoint.X}, {horizontalRoads[j].StartPoint.Y}) -> ({horizontalRoads[j].EndPoint.X}, {horizontalRoads[j].EndPoint.Y})");
                }
            }
        }

        // 检查垂直道路
        for (int i = 0; i < verticalRoads.Count; i++)
        {
            for (int j = i + 1; j < verticalRoads.Count; j++)
            {
                int xDiff = Math.Abs(verticalRoads[i].StartPoint.X - verticalRoads[j].StartPoint.X);
                if (xDiff < MergeThreshold)
                {
                    GD.PrintErr($"❌ 发现相近的垂直道路！X坐标差距: {xDiff}, 阈值: {MergeThreshold}");
                    GD.PrintErr($"道路1: ({verticalRoads[i].StartPoint.X}, {verticalRoads[i].StartPoint.Y}) -> ({verticalRoads[i].EndPoint.X}, {verticalRoads[i].EndPoint.Y})");
                    GD.PrintErr($"道路2: ({verticalRoads[j].StartPoint.X}, {verticalRoads[j].StartPoint.Y}) -> ({verticalRoads[j].EndPoint.X}, {verticalRoads[j].EndPoint.Y})");
                }
            }
        }

        GD.Print($"✅ 道路验证完成：水平道路{horizontalRoads.Count}条，垂直道路{verticalRoads.Count}条");
    }

    /// <summary>
    /// 打印所有生成的道路线段
    /// </summary>
    private void PrintRoadSegments()
    {
        GD.Print("=== 生成的道路线段详情 ===");

        var horizontalRoads = allRoadSegments.Where(s => s.IsHorizontal).ToList();
        var verticalRoads = allRoadSegments.Where(s => s.IsVertical).ToList();

        GD.Print($"📍 水平道路 ({horizontalRoads.Count} 条):");
        for (int i = 0; i < horizontalRoads.Count; i++)
        {
            var road = horizontalRoads[i];
            GD.Print($"  H{i + 1}: Y={road.StartPoint.Y}, X范围[{road.StartPoint.X} - {road.EndPoint.X}], 长度={Math.Abs(road.EndPoint.X - road.StartPoint.X)}, 宽度={road.Width}");
        }

        GD.Print($"📍 垂直道路 ({verticalRoads.Count} 条):");
        for (int i = 0; i < verticalRoads.Count; i++)
        {
            var road = verticalRoads[i];
            GD.Print($"  V{i + 1}: X={road.StartPoint.X}, Y范围[{road.StartPoint.Y} - {road.EndPoint.Y}], 长度={Math.Abs(road.EndPoint.Y - road.StartPoint.Y)}, 宽度={road.Width}");
        }

        GD.Print("========================");
    }

    /// <summary>
    /// 验证中心点连通性
    /// </summary>
    private void ValidateCenterPointConnectivity(List<delaunay_algorithm.Edge> originalEdges)
    {
        GD.Print("=== 验证中心点连通性 ===");

        // 收集所有中心点
        var centerPoints = new HashSet<Vector2I>();
        foreach (var edge in originalEdges)
        {
            centerPoints.Add(new Vector2I((int)edge.a.x, (int)edge.a.y));
            centerPoints.Add(new Vector2I((int)edge.b.x, (int)edge.b.y));
        }

        GD.Print($"需要验证的中心点数量: {centerPoints.Count}");

        int connectedCount = 0;
        int notConnectedCount = 0;

        foreach (var centerPoint in centerPoints)
        {
            bool isConnected = IsCenterPointConnected(centerPoint);
            if (isConnected)
            {
                connectedCount++;
                GD.Print($"✅ 中心点 ({centerPoint.X}, {centerPoint.Y}) 已连接到道路网络");
            }
            else
            {
                notConnectedCount++;
                GD.PrintErr($"❌ 中心点 ({centerPoint.X}, {centerPoint.Y}) 未连接到道路网络！");

                // 查找最近的道路
                var nearestRoad = FindNearestRoad(centerPoint);
                if (nearestRoad != null)
                {
                    float distance = CalculateDistanceToRoad(centerPoint, nearestRoad);
                    GD.PrintErr($"   最近道路距离: {distance:F2}");
                }
            }
        }

        GD.Print($"连通性验证结果: ✅{connectedCount}个已连接, ❌{notConnectedCount}个未连接");

        if (notConnectedCount == 0)
        {
            GD.Print("🎉 所有中心点都已正确连接到道路网络！");
        }
        else
        {
            GD.PrintErr($"⚠️ 有 {notConnectedCount} 个中心点未连接，可能需要调整道路生成算法");
        }

        GD.Print("========================");
    }

    /// <summary>
    /// 检查中心点是否连接到道路网络
    /// </summary>
    private bool IsCenterPointConnected(Vector2I centerPoint)
    {
        // 检查中心点是否在任何道路上或道路附近
        const int tolerance = 2; // 容差范围

        foreach (var segment in allRoadSegments)
        {
            if (IsPointNearRoadSegment(centerPoint, segment, tolerance))
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// 检查点是否在道路段附近
    /// </summary>
    private bool IsPointNearRoadSegment(Vector2I point, RoadSegment segment, int tolerance)
    {
        if (segment.IsHorizontal)
        {
            // 水平道路：检查Y距离和X范围
            int distanceY = Math.Abs(point.Y - segment.StartPoint.Y);
            if (distanceY > tolerance) return false;

            int minX = Math.Min(segment.StartPoint.X, segment.EndPoint.X) - tolerance;
            int maxX = Math.Max(segment.StartPoint.X, segment.EndPoint.X) + tolerance;
            return point.X >= minX && point.X <= maxX;
        }
        else if (segment.IsVertical)
        {
            // 垂直道路：检查X距离和Y范围
            int distanceX = Math.Abs(point.X - segment.StartPoint.X);
            if (distanceX > tolerance) return false;

            int minY = Math.Min(segment.StartPoint.Y, segment.EndPoint.Y) - tolerance;
            int maxY = Math.Max(segment.StartPoint.Y, segment.EndPoint.Y) + tolerance;
            return point.Y >= minY && point.Y <= maxY;
        }

        return false;
    }

    /// <summary>
    /// 查找最近的道路
    /// </summary>
    private RoadSegment FindNearestRoad(Vector2I point)
    {
        RoadSegment nearestRoad = null;
        float minDistance = float.MaxValue;

        foreach (var segment in allRoadSegments)
        {
            float distance = CalculateDistanceToRoad(point, segment);
            if (distance < minDistance)
            {
                minDistance = distance;
                nearestRoad = segment;
            }
        }

        return nearestRoad;
    }

    /// <summary>
    /// 计算点到道路的距离
    /// </summary>
    private float CalculateDistanceToRoad(Vector2I point, RoadSegment segment)
    {
        if (segment.IsHorizontal)
        {
            // 水平道路
            int roadY = segment.StartPoint.Y;
            int minX = Math.Min(segment.StartPoint.X, segment.EndPoint.X);
            int maxX = Math.Max(segment.StartPoint.X, segment.EndPoint.X);

            if (point.X >= minX && point.X <= maxX)
            {
                // 点在道路X范围内，距离就是Y方向的距离
                return Math.Abs(point.Y - roadY);
            }
            else
            {
                // 点在道路X范围外，计算到最近端点的距离
                int nearestX = point.X < minX ? minX : maxX;
                float dx = point.X - nearestX;
                float dy = point.Y - roadY;
                return Mathf.Sqrt(dx * dx + dy * dy);
            }
        }
        else if (segment.IsVertical)
        {
            // 垂直道路
            int roadX = segment.StartPoint.X;
            int minY = Math.Min(segment.StartPoint.Y, segment.EndPoint.Y);
            int maxY = Math.Max(segment.StartPoint.Y, segment.EndPoint.Y);

            if (point.Y >= minY && point.Y <= maxY)
            {
                // 点在道路Y范围内，距离就是X方向的距离
                return Math.Abs(point.X - roadX);
            }
            else
            {
                // 点在道路Y范围外，计算到最近端点的距离
                int nearestY = point.Y < minY ? minY : maxY;
                float dx = point.X - roadX;
                float dy = point.Y - nearestY;
                return Mathf.Sqrt(dx * dx + dy * dy);
            }
        }

        return float.MaxValue;
    }

    /// <summary>
    /// 生成原始道路段（L型路径，改进拐角处理）
    /// </summary>
    private List<RoadSegment> GenerateRawRoadSegments(List<delaunay_algorithm.Edge> validEdges)
    {
        var segments = new List<RoadSegment>();
        var cornerPoints = new HashSet<Vector2I>(); // 记录所有拐角点

        foreach (var edge in validEdges)
        {
            Vector2I start = new Vector2I((int)edge.a.x, (int)edge.a.y);
            Vector2I end = new Vector2I((int)edge.b.x, (int)edge.b.y);

            // 生成L型路径：水平段 + 垂直段
            if (start.X != end.X)
            {
                // 水平段
                segments.Add(new RoadSegment
                {
                    StartPoint = start,
                    EndPoint = new Vector2I(end.X, start.Y),
                    Width = DefaultRoadWidth,
                    Type = RoadType.MainRoad
                });
            }

            if (start.Y != end.Y)
            {
                // 垂直段
                segments.Add(new RoadSegment
                {
                    StartPoint = new Vector2I(end.X, start.Y),
                    EndPoint = end,
                    Width = DefaultRoadWidth,
                    Type = RoadType.MainRoad
                });
            }

            // 记录拐角点（如果既有水平又有垂直段）
            if (start.X != end.X && start.Y != end.Y)
            {
                cornerPoints.Add(new Vector2I(end.X, start.Y));
            }
        }

        // 不再生成额外的拐角段，改用查询时的端点扩展处理

        return segments;
    }



    /// <summary>
    /// 优化道路段（保守安全的合并策略）
    /// </summary>
    private List<RoadSegment> OptimizeRoadSegments(List<RoadSegment> rawSegments)
    {
        GD.Print("开始安全道路合并...");

        // 1. 找出所有连接点（端点）
        var connectionPoints = FindAllConnectionPoints(rawSegments);
        GD.Print($"发现 {connectionPoints.Count} 个连接点");

        var horizontalRoads = rawSegments.Where(s => s.IsHorizontal).ToList();
        var verticalRoads = rawSegments.Where(s => s.IsVertical).ToList();

        var optimizedSegments = new List<RoadSegment>();

        // 2. 使用安全合并策略
        optimizedSegments.AddRange(SafeMergeRoads(horizontalRoads, connectionPoints, true));
        optimizedSegments.AddRange(SafeMergeRoads(verticalRoads, connectionPoints, false));

        int originalCount = rawSegments.Count;
        int finalCount = optimizedSegments.Count;
        float mergeRatio = (float)(originalCount - finalCount) / originalCount;

        GD.Print($"安全合并完成: {originalCount} -> {finalCount} 道路段 (合并率: {mergeRatio:P1})");

        // 3. 验证连通性
        VerifyConnectivity(optimizedSegments, connectionPoints);

        // 4. 检查是否还有相近道路
        CheckRemainingNearbyRoads(optimizedSegments);

        return optimizedSegments;
    }

    /// <summary>
    /// 合并平行道路（强力版本）
    /// </summary>
    private List<RoadSegment> MergeParallelRoads(List<RoadSegment> roads, bool isHorizontal)
    {
        if (roads.Count <= 1) return roads;

        var current = new List<RoadSegment>(roads);
        bool hasChanges = true;
        int iterations = 0;
        const int maxIterations = 10;

        GD.Print($"开始合并{(isHorizontal ? "水平" : "垂直")}道路，初始数量: {roads.Count}");

        while (hasChanges && iterations < maxIterations)
        {
            hasChanges = false;
            iterations++;
            var newList = new List<RoadSegment>();
            int mergeCount = 0;

            for (int i = 0; i < current.Count; i++)
            {
                var roadA = current[i];
                bool merged = false;

                // 尝试与newList中的道路合并
                for (int j = 0; j < newList.Count; j++)
                {
                    var roadB = newList[j];
                    if (ShouldMergeRoads(roadA, roadB, isHorizontal))
                    {
                        // 合并两条道路
                        var mergedRoad = MergeRoadGroup(new List<RoadSegment> { roadA, roadB }, isHorizontal);
                        newList[j] = mergedRoad;
                        merged = true;
                        hasChanges = true;
                        mergeCount++;

                        // 调试信息
                        if (isHorizontal)
                        {
                            GD.Print($"  合并水平道路: Y={roadA.StartPoint.Y} & Y={roadB.StartPoint.Y} -> Y={mergedRoad.StartPoint.Y}");
                        }
                        else
                        {
                            GD.Print($"  合并垂直道路: X={roadA.StartPoint.X} & X={roadB.StartPoint.X} -> X={mergedRoad.StartPoint.X}");
                        }
                        break;
                    }
                }

                if (!merged)
                {
                    newList.Add(roadA);
                }
            }

            current = newList;
            GD.Print($"{(isHorizontal ? "水平" : "垂直")}道路合并第{iterations}轮：{current.Count}条道路 (合并了{mergeCount}次)");
        }

        return current;
    }

    /// <summary>
    /// 找出所有关键连接点（交点和端点）
    /// </summary>
    private HashSet<Vector2I> FindCriticalConnectionPoints(List<RoadSegment> segments)
    {
        var criticalPoints = new HashSet<Vector2I>();

        // 1. 添加所有端点
        foreach (var segment in segments)
        {
            criticalPoints.Add(segment.StartPoint);
            criticalPoints.Add(segment.EndPoint);
        }

        // 2. 找出真正的交点（被多条道路共享的点）
        var pointUsageCount = new Dictionary<Vector2I, int>();
        foreach (var point in criticalPoints)
        {
            pointUsageCount[point] = 0;
            foreach (var segment in segments)
            {
                if (segment.StartPoint == point || segment.EndPoint == point)
                {
                    pointUsageCount[point]++;
                }
            }
        }

        // 3. 只保留被多条道路使用的点（真正的交点）
        var realCriticalPoints = new HashSet<Vector2I>();
        foreach (var kvp in pointUsageCount)
        {
            if (kvp.Value > 1) // 被多条道路共享
            {
                realCriticalPoints.Add(kvp.Key);
            }
        }

        GD.Print($"关键点分析: 总端点 {criticalPoints.Count}, 真正交点 {realCriticalPoints.Count}");
        return realCriticalPoints;
    }

    /// <summary>
    /// 智能合并道路，保持与关键点的连接
    /// </summary>
    private List<RoadSegment> MergeRoadsPreservingConnections(List<RoadSegment> roads, HashSet<Vector2I> criticalPoints, bool isHorizontal)
    {
        if (roads.Count <= 1) return roads;

        GD.Print($"开始智能合并{(isHorizontal ? "水平" : "垂直")}道路: {roads.Count}条");

        // 1. 按坐标分组（水平道路按Y坐标，垂直道路按X坐标）
        var roadGroups = new Dictionary<int, List<RoadSegment>>();

        foreach (var road in roads)
        {
            int key = isHorizontal ? road.StartPoint.Y : road.StartPoint.X;
            if (!roadGroups.ContainsKey(key))
                roadGroups[key] = new List<RoadSegment>();
            roadGroups[key].Add(road);
        }

        var result = new List<RoadSegment>();

        // 2. 对每组道路进行智能合并
        foreach (var group in roadGroups)
        {
            var groupRoads = group.Value;
            if (groupRoads.Count == 1)
            {
                result.AddRange(groupRoads);
                continue;
            }

            // 3. 在这组道路中找出所有关键分割点
            var splitPoints = FindSplitPoints(groupRoads, criticalPoints, isHorizontal);

            // 4. 根据分割点创建连续的道路段
            var mergedRoads = CreateMergedSegments(groupRoads, splitPoints, isHorizontal);
            result.AddRange(mergedRoads);

            GD.Print($"  坐标{group.Key}: {groupRoads.Count}条道路 -> {mergedRoads.Count}条道路 (分割点: {splitPoints.Count})");
        }

        return result;
    }

    /// <summary>
    /// 找出需要保持的分割点（优化版：只保留真正重要的点）
    /// </summary>
    private List<int> FindSplitPoints(List<RoadSegment> roads, HashSet<Vector2I> criticalPoints, bool isHorizontal)
    {
        var splitPoints = new HashSet<int>();

        // 只添加真正的关键交点，不添加所有端点
        foreach (var road in roads)
        {
            if (criticalPoints.Contains(road.StartPoint))
            {
                int coord = isHorizontal ? road.StartPoint.X : road.StartPoint.Y;
                splitPoints.Add(coord);
            }
            if (criticalPoints.Contains(road.EndPoint))
            {
                int coord = isHorizontal ? road.EndPoint.X : road.EndPoint.Y;
                splitPoints.Add(coord);
            }
        }

        // 如果没有关键点，尝试简单合并
        if (splitPoints.Count == 0)
        {
            GD.Print($"  没有关键分割点，可以完全合并");
            return new List<int>();
        }

        var result = splitPoints.OrderBy(x => x).ToList();
        GD.Print($"  找到 {result.Count} 个关键分割点: [{string.Join(", ", result)}]");
        return result;
    }

    /// <summary>
    /// 根据分割点创建合并的道路段（优化版：更积极的合并）
    /// </summary>
    private List<RoadSegment> CreateMergedSegments(List<RoadSegment> roads, List<int> splitPoints, bool isHorizontal)
    {
        if (splitPoints.Count == 0)
        {
            // 没有分割点，可以完全合并
            GD.Print($"    完全合并 {roads.Count} 条道路");
            return new List<RoadSegment> { MergeRoadGroup(roads, isHorizontal) };
        }

        // 获取所有道路的范围
        var allRanges = roads.Select(road => new
        {
            Start = isHorizontal ? Math.Min(road.StartPoint.X, road.EndPoint.X) : Math.Min(road.StartPoint.Y, road.EndPoint.Y),
            End = isHorizontal ? Math.Max(road.StartPoint.X, road.EndPoint.X) : Math.Max(road.StartPoint.Y, road.EndPoint.Y)
        }).ToList();

        int overallStart = allRanges.Min(r => r.Start);
        int overallEnd = allRanges.Max(r => r.End);

        // 添加整体范围的边界到分割点
        var extendedSplitPoints = new List<int>(splitPoints);
        if (!extendedSplitPoints.Contains(overallStart))
            extendedSplitPoints.Add(overallStart);
        if (!extendedSplitPoints.Contains(overallEnd))
            extendedSplitPoints.Add(overallEnd);

        extendedSplitPoints.Sort();

        var result = new List<RoadSegment>();
        int fixedCoord = FindMostCommonCoordinate(roads.Select(r => isHorizontal ? r.StartPoint.Y : r.StartPoint.X));

        GD.Print($"    分割点: [{string.Join(", ", extendedSplitPoints)}], 固定坐标: {fixedCoord}");

        // 创建连续的道路段，确保没有缺口
        var coveredRanges = new List<(int start, int end)>();

        // 首先收集所有有道路覆盖的区间
        for (int i = 0; i < extendedSplitPoints.Count - 1; i++)
        {
            int start = extendedSplitPoints[i];
            int end = extendedSplitPoints[i + 1];

            // 检查这个区间是否有道路覆盖
            bool hasOverlap = roads.Any(road =>
            {
                int roadStart = isHorizontal ? Math.Min(road.StartPoint.X, road.EndPoint.X) : Math.Min(road.StartPoint.Y, road.EndPoint.Y);
                int roadEnd = isHorizontal ? Math.Max(road.StartPoint.X, road.EndPoint.X) : Math.Max(road.StartPoint.Y, road.EndPoint.Y);
                return roadEnd >= start && roadStart <= end;
            });

            if (hasOverlap && end > start)
            {
                coveredRanges.Add((start, end));
            }
        }

        // 合并相邻的区间，避免缺口
        var mergedRanges = MergeAdjacentRanges(coveredRanges);

        // 为每个合并后的区间创建道路段
        foreach (var range in mergedRanges)
        {
            var segment = new RoadSegment
            {
                Width = roads.Max(r => r.Width),
                Type = roads.First().Type
            };

            if (isHorizontal)
            {
                segment.StartPoint = new Vector2I(range.start, fixedCoord);
                segment.EndPoint = new Vector2I(range.end, fixedCoord);
            }
            else
            {
                segment.StartPoint = new Vector2I(fixedCoord, range.start);
                segment.EndPoint = new Vector2I(fixedCoord, range.end);
            }

            result.Add(segment);
            GD.Print($"      创建连续段: {segment.StartPoint} -> {segment.EndPoint}");
        }

        GD.Print($"    分段合并: {roads.Count} -> {result.Count} 条道路");
        return result;
    }

    /// <summary>
    /// 合并相邻的区间，避免缺口
    /// </summary>
    private List<(int start, int end)> MergeAdjacentRanges(List<(int start, int end)> ranges)
    {
        if (ranges.Count <= 1) return ranges;

        // 按起始位置排序
        var sortedRanges = ranges.OrderBy(r => r.start).ToList();
        var merged = new List<(int start, int end)>();

        var current = sortedRanges[0];

        for (int i = 1; i < sortedRanges.Count; i++)
        {
            var next = sortedRanges[i];

            // 如果当前区间的结束位置 >= 下一个区间的开始位置，则合并
            // 允许小的间隙（比如1-2个像素）
            if (current.end >= next.start - 2)
            {
                // 合并区间
                current = (current.start, Math.Max(current.end, next.end));
            }
            else
            {
                // 不能合并，添加当前区间并开始新的区间
                merged.Add(current);
                current = next;
            }
        }

        // 添加最后一个区间
        merged.Add(current);

        GD.Print($"      区间合并: {ranges.Count} -> {merged.Count} 个区间");
        return merged;
    }

    /// <summary>
    /// 检查道路段中的缺口
    /// </summary>
    private int CheckForGaps(List<RoadSegment> segments)
    {
        int gapCount = 0;

        // 分别检查水平和垂直道路
        var horizontalRoads = segments.Where(s => s.IsHorizontal).GroupBy(s => s.StartPoint.Y);
        var verticalRoads = segments.Where(s => s.IsVertical).GroupBy(s => s.StartPoint.X);

        foreach (var group in horizontalRoads)
        {
            var roads = group.OrderBy(r => Math.Min(r.StartPoint.X, r.EndPoint.X)).ToList();
            for (int i = 0; i < roads.Count - 1; i++)
            {
                int currentEnd = Math.Max(roads[i].StartPoint.X, roads[i].EndPoint.X);
                int nextStart = Math.Min(roads[i + 1].StartPoint.X, roads[i + 1].EndPoint.X);
                if (nextStart > currentEnd + 2) // 有缺口
                {
                    gapCount++;
                    GD.Print($"  发现水平缺口: Y={group.Key}, X={currentEnd} 到 {nextStart}");
                }
            }
        }

        foreach (var group in verticalRoads)
        {
            var roads = group.OrderBy(r => Math.Min(r.StartPoint.Y, r.EndPoint.Y)).ToList();
            for (int i = 0; i < roads.Count - 1; i++)
            {
                int currentEnd = Math.Max(roads[i].StartPoint.Y, roads[i].EndPoint.Y);
                int nextStart = Math.Min(roads[i + 1].StartPoint.Y, roads[i + 1].EndPoint.Y);
                if (nextStart > currentEnd + 2) // 有缺口
                {
                    gapCount++;
                    GD.Print($"  发现垂直缺口: X={group.Key}, Y={currentEnd} 到 {nextStart}");
                }
            }
        }

        return gapCount;
    }

    /// <summary>
    /// 简单的分组合并（最保守的策略）
    /// </summary>
    private List<RoadSegment> SimpleGroupMerge(List<RoadSegment> roads, bool isHorizontal)
    {
        if (roads.Count == 0) return roads;

        var groups = roads.GroupBy(r => isHorizontal ? r.StartPoint.Y : r.StartPoint.X);
        var result = new List<RoadSegment>();

        foreach (var group in groups)
        {
            // 每组完全合并成一条道路
            var groupRoads = group.ToList();
            var merged = MergeRoadGroup(groupRoads, isHorizontal);
            result.Add(merged);

            GD.Print($"  简单合并: 坐标{group.Key} - {groupRoads.Count}条 -> 1条");
        }

        return result;
    }

    /// <summary>
    /// 简单有效的按坐标合并策略
    /// </summary>
    private List<RoadSegment> SimpleMergeByCoordinate(List<RoadSegment> roads, bool isHorizontal)
    {
        if (roads.Count <= 1) return roads;

        GD.Print($"开始合并{(isHorizontal ? "水平" : "垂直")}道路: {roads.Count}条");

        // 1. 按固定坐标分组（水平道路按Y坐标，垂直道路按X坐标）
        var groups = roads.GroupBy(road =>
            isHorizontal ? road.StartPoint.Y : road.StartPoint.X
        ).ToList();

        var result = new List<RoadSegment>();

        // 2. 对每组道路进行处理
        foreach (var group in groups)
        {
            var groupRoads = group.ToList();
            int fixedCoord = group.Key;

            if (groupRoads.Count == 1)
            {
                // 只有一条道路，直接添加
                result.AddRange(groupRoads);
                GD.Print($"  坐标{fixedCoord}: 1条道路，保持不变");
            }
            else
            {
                // 多条道路，检查是否在阈值范围内
                var coordVariations = groupRoads.Select(r =>
                    isHorizontal ? r.StartPoint.Y : r.StartPoint.X
                ).Distinct().ToList();

                int maxVariation = coordVariations.Max() - coordVariations.Min();

                if (maxVariation <= MergeThreshold)
                {
                    // 坐标变化在阈值内，可以合并
                    var merged = CreateMergedRoad(groupRoads, isHorizontal);
                    result.Add(merged);
                    GD.Print($"  坐标{fixedCoord}: {groupRoads.Count}条道路合并为1条 (变化范围: {maxVariation})");
                }
                else
                {
                    // 坐标变化太大，不合并
                    result.AddRange(groupRoads);
                    GD.Print($"  坐标{fixedCoord}: {groupRoads.Count}条道路保持独立 (变化范围: {maxVariation} > {MergeThreshold})");
                }
            }
        }

        GD.Print($"合并{(isHorizontal ? "水平" : "垂直")}完成: {roads.Count} -> {result.Count}");
        return result;
    }

    /// <summary>
    /// 创建合并后的道路
    /// </summary>
    private RoadSegment CreateMergedRoad(List<RoadSegment> roads, bool isHorizontal)
    {
        // 使用最常见的固定坐标
        int bestFixedCoord = FindMostCommonCoordinate(roads.Select(r =>
            isHorizontal ? r.StartPoint.Y : r.StartPoint.X
        ));

        // 找出变化坐标的范围
        int minVarCoord = roads.Min(r => isHorizontal ?
            Math.Min(r.StartPoint.X, r.EndPoint.X) :
            Math.Min(r.StartPoint.Y, r.EndPoint.Y));
        int maxVarCoord = roads.Max(r => isHorizontal ?
            Math.Max(r.StartPoint.X, r.EndPoint.X) :
            Math.Max(r.StartPoint.Y, r.EndPoint.Y));

        var merged = new RoadSegment
        {
            Width = roads.Max(r => r.Width),
            Type = roads.First().Type
        };

        if (isHorizontal)
        {
            merged.StartPoint = new Vector2I(minVarCoord, bestFixedCoord);
            merged.EndPoint = new Vector2I(maxVarCoord, bestFixedCoord);
        }
        else
        {
            merged.StartPoint = new Vector2I(bestFixedCoord, minVarCoord);
            merged.EndPoint = new Vector2I(bestFixedCoord, maxVarCoord);
        }

        return merged;
    }

    /// <summary>
    /// 后处理：检查并合并剩余的平行相近道路
    /// </summary>
    private List<RoadSegment> PostProcessParallelRoads(List<RoadSegment> roads)
    {
        GD.Print("开始后处理：检查平行相近道路...");

        var horizontalRoads = roads.Where(s => s.IsHorizontal).ToList();
        var verticalRoads = roads.Where(s => s.IsVertical).ToList();

        var result = new List<RoadSegment>();

        // 处理水平道路
        result.AddRange(MergeParallelNearbyRoads(horizontalRoads, true));

        // 处理垂直道路
        result.AddRange(MergeParallelNearbyRoads(verticalRoads, false));

        int mergedCount = roads.Count - result.Count;
        if (mergedCount > 0)
        {
            GD.Print($"后处理完成：额外合并了 {mergedCount} 条平行道路");
        }
        else
        {
            GD.Print("后处理完成：没有发现需要合并的平行道路");
        }

        return result;
    }

    /// <summary>
    /// 合并平行且相近的道路
    /// </summary>
    private List<RoadSegment> MergeParallelNearbyRoads(List<RoadSegment> roads, bool isHorizontal)
    {
        if (roads.Count <= 1) return roads;

        var result = new List<RoadSegment>(roads);
        bool hasChanges = true;
        int iteration = 0;

        while (hasChanges && iteration < 5) // 最多迭代5次
        {
            hasChanges = false;
            iteration++;
            var newResult = new List<RoadSegment>();

            for (int i = 0; i < result.Count; i++)
            {
                var roadA = result[i];
                bool merged = false;

                // 尝试与已处理的道路合并
                for (int j = 0; j < newResult.Count; j++)
                {
                    var roadB = newResult[j];

                    if (AreParallelAndNearby(roadA, roadB, isHorizontal))
                    {
                        // 合并这两条道路
                        var mergedRoad = MergeTwoParallelRoads(roadA, roadB, isHorizontal);
                        newResult[j] = mergedRoad;
                        merged = true;
                        hasChanges = true;

                        GD.Print($"  后处理合并: {roadA.StartPoint}->{roadA.EndPoint} + {roadB.StartPoint}->{roadB.EndPoint} = {mergedRoad.StartPoint}->{mergedRoad.EndPoint}");
                        break;
                    }
                }

                if (!merged)
                {
                    newResult.Add(roadA);
                }
            }

            result = newResult;
        }

        return result;
    }

    /// <summary>
    /// 检查两条道路是否平行且相近
    /// </summary>
    private bool AreParallelAndNearby(RoadSegment road1, RoadSegment road2, bool isHorizontal)
    {
        // 必须都是同一方向的道路
        if (road1.IsHorizontal != road2.IsHorizontal || road1.IsHorizontal != isHorizontal)
            return false;

        if (isHorizontal)
        {
            // 水平道路：检查Y坐标距离
            int yDistance = Math.Abs(road1.StartPoint.Y - road2.StartPoint.Y);
            if (yDistance > MergeThreshold) return false;

            // 检查X轴是否有重叠或相近
            int road1MinX = Math.Min(road1.StartPoint.X, road1.EndPoint.X);
            int road1MaxX = Math.Max(road1.StartPoint.X, road1.EndPoint.X);
            int road2MinX = Math.Min(road2.StartPoint.X, road2.EndPoint.X);
            int road2MaxX = Math.Max(road2.StartPoint.X, road2.EndPoint.X);

            // 检查是否有重叠或间隙很小
            bool hasOverlap = road1MaxX >= road2MinX && road2MaxX >= road1MinX;
            if (hasOverlap) return true;

            int gap = Math.Min(Math.Abs(road1MinX - road2MaxX), Math.Abs(road2MinX - road1MaxX));
            return gap <= MergeThreshold;
        }
        else
        {
            // 垂直道路：检查X坐标距离
            int xDistance = Math.Abs(road1.StartPoint.X - road2.StartPoint.X);
            if (xDistance > MergeThreshold) return false;

            // 检查Y轴是否有重叠或相近
            int road1MinY = Math.Min(road1.StartPoint.Y, road1.EndPoint.Y);
            int road1MaxY = Math.Max(road1.StartPoint.Y, road1.EndPoint.Y);
            int road2MinY = Math.Min(road2.StartPoint.Y, road2.EndPoint.Y);
            int road2MaxY = Math.Max(road2.StartPoint.Y, road2.EndPoint.Y);

            // 检查是否有重叠或间隙很小
            bool hasOverlap = road1MaxY >= road2MinY && road2MaxY >= road1MinY;
            if (hasOverlap) return true;

            int gap = Math.Min(Math.Abs(road1MinY - road2MaxY), Math.Abs(road2MinY - road1MaxY));
            return gap <= MergeThreshold;
        }
    }

    /// <summary>
    /// 合并两条平行道路
    /// </summary>
    private RoadSegment MergeTwoParallelRoads(RoadSegment road1, RoadSegment road2, bool isHorizontal)
    {
        var roads = new List<RoadSegment> { road1, road2 };
        return CreateMergedRoad(roads, isHorizontal);
    }

    /// <summary>
    /// 找出所有连接点
    /// </summary>
    private HashSet<Vector2I> FindAllConnectionPoints(List<RoadSegment> segments)
    {
        var points = new HashSet<Vector2I>();

        foreach (var segment in segments)
        {
            points.Add(segment.StartPoint);
            points.Add(segment.EndPoint);
        }

        // 只保留被多条道路共享的点（真正的连接点）
        var connectionPoints = new HashSet<Vector2I>();
        foreach (var point in points)
        {
            int count = 0;
            foreach (var segment in segments)
            {
                if (segment.StartPoint == point || segment.EndPoint == point)
                {
                    count++;
                    if (count > 1)
                    {
                        connectionPoints.Add(point);
                        break;
                    }
                }
            }
        }

        return connectionPoints;
    }

    /// <summary>
    /// 安全合并道路（保证不破坏连接点）
    /// </summary>
    private List<RoadSegment> SafeMergeRoads(List<RoadSegment> roads, HashSet<Vector2I> connectionPoints, bool isHorizontal)
    {
        if (roads.Count <= 1) return roads;

        GD.Print($"开始安全合并{(isHorizontal ? "水平" : "垂直")}道路: {roads.Count}条");

        // 1. 按精确坐标分组（不允许任何偏差）
        var exactGroups = roads.GroupBy(road =>
            isHorizontal ? road.StartPoint.Y : road.StartPoint.X
        ).ToList();

        var result = new List<RoadSegment>();

        foreach (var group in exactGroups)
        {
            var groupRoads = group.ToList();
            int fixedCoord = group.Key;

            if (groupRoads.Count == 1)
            {
                result.AddRange(groupRoads);
                continue;
            }

            // 2. 检查这组道路是否可以安全合并
            if (CanSafelyMerge(groupRoads, connectionPoints, isHorizontal))
            {
                var merged = CreateSafeMergedRoad(groupRoads, connectionPoints, isHorizontal);
                result.Add(merged);
                GD.Print($"  安全合并坐标{fixedCoord}: {groupRoads.Count}条 -> 1条");
            }
            else
            {
                result.AddRange(groupRoads);
                GD.Print($"  保持独立坐标{fixedCoord}: {groupRoads.Count}条 (有连接点冲突)");
            }
        }

        return result;
    }

    /// <summary>
    /// 检查是否可以安全合并（不破坏连接点）
    /// </summary>
    private bool CanSafelyMerge(List<RoadSegment> roads, HashSet<Vector2I> connectionPoints, bool isHorizontal)
    {
        // 收集所有端点
        var allEndpoints = new HashSet<Vector2I>();
        foreach (var road in roads)
        {
            allEndpoints.Add(road.StartPoint);
            allEndpoints.Add(road.EndPoint);
        }

        // 检查是否有连接点在这组道路的中间（不是端点）
        int minVar = roads.Min(r => isHorizontal ?
            Math.Min(r.StartPoint.X, r.EndPoint.X) :
            Math.Min(r.StartPoint.Y, r.EndPoint.Y));
        int maxVar = roads.Max(r => isHorizontal ?
            Math.Max(r.StartPoint.X, r.EndPoint.X) :
            Math.Max(r.StartPoint.Y, r.EndPoint.Y));

        int fixedCoord = isHorizontal ? roads[0].StartPoint.Y : roads[0].StartPoint.X;

        // 检查在合并范围内是否有其他连接点
        foreach (var point in connectionPoints)
        {
            int pointFixed = isHorizontal ? point.Y : point.X;
            int pointVar = isHorizontal ? point.X : point.Y;

            // 如果连接点在同一条线上，且在合并范围内，但不是端点
            if (pointFixed == fixedCoord && pointVar > minVar && pointVar < maxVar)
            {
                if (!allEndpoints.Contains(point))
                {
                    GD.Print($"    发现中间连接点: {point}，不能安全合并");
                    return false;
                }
            }
        }

        return true;
    }

    /// <summary>
    /// 创建安全合并的道路（保留所有连接点）
    /// </summary>
    private RoadSegment CreateSafeMergedRoad(List<RoadSegment> roads, HashSet<Vector2I> connectionPoints, bool isHorizontal)
    {
        // 使用原有的合并逻辑，因为已经确认安全
        return CreateMergedRoad(roads, isHorizontal);
    }

    /// <summary>
    /// 验证连通性
    /// </summary>
    private void VerifyConnectivity(List<RoadSegment> segments, HashSet<Vector2I> originalConnectionPoints)
    {
        GD.Print("验证道路连通性...");

        var newConnectionPoints = FindAllConnectionPoints(segments);

        int lostConnections = 0;
        foreach (var point in originalConnectionPoints)
        {
            if (!newConnectionPoints.Contains(point))
            {
                // 检查这个点是否仍然被道路覆盖
                bool covered = false;
                foreach (var segment in segments)
                {
                    if (IsPointOnRoadSegment(point, segment))
                    {
                        covered = true;
                        break;
                    }
                }

                if (!covered)
                {
                    lostConnections++;
                    GD.Print($"  ⚠ 丢失连接点: {point}");
                }
            }
        }

        if (lostConnections == 0)
        {
            GD.Print("  ✓ 所有连接点都保持完好");
        }
        else
        {
            GD.Print($"  ⚠ 丢失了 {lostConnections} 个连接点");
        }
    }

    /// <summary>
    /// 检查合并后是否还有相近道路
    /// </summary>
    private void CheckRemainingNearbyRoads(List<RoadSegment> segments)
    {
        GD.Print("检查剩余的相近道路...");

        var horizontalRoads = segments.Where(s => s.IsHorizontal).ToList();
        var verticalRoads = segments.Where(s => s.IsVertical).ToList();

        // 检查水平道路中的相近道路
        int nearbyHorizontalCount = CheckNearbyRoadsInGroup(horizontalRoads, true);

        // 检查垂直道路中的相近道路
        int nearbyVerticalCount = CheckNearbyRoadsInGroup(verticalRoads, false);

        int totalNearbyRoads = nearbyHorizontalCount + nearbyVerticalCount;

        if (totalNearbyRoads > 0)
        {
            GD.Print($"  ⚠ 发现 {totalNearbyRoads} 对相近道路 (水平: {nearbyHorizontalCount}, 垂直: {nearbyVerticalCount})");
            GD.Print($"  建议调整 MergeThreshold (当前: {MergeThreshold}) 或优化合并算法");
        }
        else
        {
            GD.Print("  ✓ 没有发现相近道路，合并效果良好");
        }
    }

    /// <summary>
    /// 检查一组道路中的相近道路
    /// </summary>
    private int CheckNearbyRoadsInGroup(List<RoadSegment> roads, bool isHorizontal)
    {
        if (roads.Count <= 1) return 0;

        int nearbyCount = 0;
        int checkThreshold = MergeThreshold; // 使用当前的合并阈值作为检查阈值

        for (int i = 0; i < roads.Count; i++)
        {
            for (int j = i + 1; j < roads.Count; j++)
            {
                var road1 = roads[i];
                var road2 = roads[j];

                if (AreRoadsNearbyForCheck(road1, road2, isHorizontal, checkThreshold))
                {
                    nearbyCount++;

                    if (isHorizontal)
                    {
                        int y1 = road1.StartPoint.Y;
                        int y2 = road2.StartPoint.Y;
                        int distance = Math.Abs(y1 - y2);
                        GD.Print($"    相近水平道路: Y={y1} 和 Y={y2}, 距离={distance}");
                    }
                    else
                    {
                        int x1 = road1.StartPoint.X;
                        int x2 = road2.StartPoint.X;
                        int distance = Math.Abs(x1 - x2);
                        GD.Print($"    相近垂直道路: X={x1} 和 X={x2}, 距离={distance}");
                    }
                }
            }
        }

        return nearbyCount;
    }

    /// <summary>
    /// 检查两条道路是否相近（用于最终检查）
    /// </summary>
    private bool AreRoadsNearbyForCheck(RoadSegment road1, RoadSegment road2, bool isHorizontal, int threshold)
    {
        // 必须是同方向的道路
        if (road1.IsHorizontal != road2.IsHorizontal || road1.IsHorizontal != isHorizontal)
            return false;

        if (isHorizontal)
        {
            // 水平道路：检查Y坐标距离
            int yDistance = Math.Abs(road1.StartPoint.Y - road2.StartPoint.Y);
            if (yDistance > threshold) return false;

            // 检查X轴是否有重叠
            int road1MinX = Math.Min(road1.StartPoint.X, road1.EndPoint.X);
            int road1MaxX = Math.Max(road1.StartPoint.X, road1.EndPoint.X);
            int road2MinX = Math.Min(road2.StartPoint.X, road2.EndPoint.X);
            int road2MaxX = Math.Max(road2.StartPoint.X, road2.EndPoint.X);

            // 有重叠才算相近
            return road1MaxX >= road2MinX && road2MaxX >= road1MinX;
        }
        else
        {
            // 垂直道路：检查X坐标距离
            int xDistance = Math.Abs(road1.StartPoint.X - road2.StartPoint.X);
            if (xDistance > threshold) return false;

            // 检查Y轴是否有重叠
            int road1MinY = Math.Min(road1.StartPoint.Y, road1.EndPoint.Y);
            int road1MaxY = Math.Max(road1.StartPoint.Y, road1.EndPoint.Y);
            int road2MinY = Math.Min(road2.StartPoint.Y, road2.EndPoint.Y);
            int road2MaxY = Math.Max(road2.StartPoint.Y, road2.EndPoint.Y);

            // 有重叠才算相近
            return road1MaxY >= road2MinY && road2MaxY >= road1MinY;
        }
    }

    /// <summary>
    /// 智能的组内合并
    /// </summary>
    private List<RoadSegment> SmartGroupMerge(List<RoadSegment> roads, bool isHorizontal, int fixedCoord)
    {
        // 1. 按位置排序
        var sortedRoads = roads.OrderBy(r =>
            isHorizontal ? Math.Min(r.StartPoint.X, r.EndPoint.X) : Math.Min(r.StartPoint.Y, r.EndPoint.Y)
        ).ToList();

        // 2. 找出连续的道路段
        var continuousGroups = new List<List<RoadSegment>>();
        var currentGroup = new List<RoadSegment> { sortedRoads[0] };

        for (int i = 1; i < sortedRoads.Count; i++)
        {
            var prevRoad = sortedRoads[i - 1];
            var currentRoad = sortedRoads[i];

            int prevEnd = isHorizontal ?
                Math.Max(prevRoad.StartPoint.X, prevRoad.EndPoint.X) :
                Math.Max(prevRoad.StartPoint.Y, prevRoad.EndPoint.Y);
            int currentStart = isHorizontal ?
                Math.Min(currentRoad.StartPoint.X, currentRoad.EndPoint.X) :
                Math.Min(currentRoad.StartPoint.Y, currentRoad.EndPoint.Y);

            // 如果间隙小于阈值，认为是连续的
            if (currentStart <= prevEnd + MergeThreshold)
            {
                currentGroup.Add(currentRoad);
            }
            else
            {
                // 开始新的连续组
                continuousGroups.Add(currentGroup);
                currentGroup = new List<RoadSegment> { currentRoad };
            }
        }
        continuousGroups.Add(currentGroup);

        // 3. 合并每个连续组
        var result = new List<RoadSegment>();
        foreach (var group in continuousGroups)
        {
            if (group.Count == 1)
            {
                result.AddRange(group);
            }
            else
            {
                // 合并连续的道路段
                var merged = CreateContinuousSegment(group, isHorizontal, fixedCoord);
                result.Add(merged);

                GD.Print($"    合并连续段: {group.Count}条 -> 1条 ({merged.StartPoint} -> {merged.EndPoint})");
            }
        }

        return result;
    }

    /// <summary>
    /// 创建连续的道路段
    /// </summary>
    private RoadSegment CreateContinuousSegment(List<RoadSegment> roads, bool isHorizontal, int fixedCoord)
    {
        int minCoord = roads.Min(r => isHorizontal ?
            Math.Min(r.StartPoint.X, r.EndPoint.X) :
            Math.Min(r.StartPoint.Y, r.EndPoint.Y));
        int maxCoord = roads.Max(r => isHorizontal ?
            Math.Max(r.StartPoint.X, r.EndPoint.X) :
            Math.Max(r.StartPoint.Y, r.EndPoint.Y));

        var segment = new RoadSegment
        {
            Width = roads.Max(r => r.Width),
            Type = roads.First().Type
        };

        if (isHorizontal)
        {
            segment.StartPoint = new Vector2I(minCoord, fixedCoord);
            segment.EndPoint = new Vector2I(maxCoord, fixedCoord);
        }
        else
        {
            segment.StartPoint = new Vector2I(fixedCoord, minCoord);
            segment.EndPoint = new Vector2I(fixedCoord, maxCoord);
        }

        return segment;
    }

    /// <summary>
    /// 判断两条道路是否应该合并（更严格的条件，避免不合理的合并）
    /// </summary>
    private bool ShouldMergeRoads(RoadSegment road1, RoadSegment road2, bool isHorizontal)
    {
        if (isHorizontal)
        {
            // 检查Y坐标是否相近
            int yDiff = Math.Abs(road1.StartPoint.Y - road2.StartPoint.Y);
            if (yDiff >= MergeThreshold) return false;

            // 检查X轴是否有重叠或相邻
            int road1MinX = Math.Min(road1.StartPoint.X, road1.EndPoint.X);
            int road1MaxX = Math.Max(road1.StartPoint.X, road1.EndPoint.X);
            int road2MinX = Math.Min(road2.StartPoint.X, road2.EndPoint.X);
            int road2MaxX = Math.Max(road2.StartPoint.X, road2.EndPoint.X);

            // 检查X轴是否有重叠或相邻（修复间隙计算）
            // 如果两个道路段有重叠或间隙小于阈值，则可以合并
            bool hasOverlap = road1MaxX >= road2MinX && road2MaxX >= road1MinX;
            if (hasOverlap) return true;

            // 检查间隙
            int gap = Math.Min(Math.Abs(road1MinX - road2MaxX), Math.Abs(road2MinX - road1MaxX));
            return gap <= MergeThreshold;
        }
        else
        {
            // 检查X坐标是否相近
            int xDiff = Math.Abs(road1.StartPoint.X - road2.StartPoint.X);
            if (xDiff >= MergeThreshold) return false;

            // 检查Y轴是否有重叠或相邻
            int road1MinY = Math.Min(road1.StartPoint.Y, road1.EndPoint.Y);
            int road1MaxY = Math.Max(road1.StartPoint.Y, road1.EndPoint.Y);
            int road2MinY = Math.Min(road2.StartPoint.Y, road2.EndPoint.Y);
            int road2MaxY = Math.Max(road2.StartPoint.Y, road2.EndPoint.Y);

            // 检查Y轴是否有重叠或相邻（修复间隙计算）
            // 如果两个道路段有重叠或间隙小于阈值，则可以合并
            bool hasOverlap = road1MaxY >= road2MinY && road2MaxY >= road1MinY;
            if (hasOverlap) return true;

            // 检查间隙
            int gap = Math.Min(Math.Abs(road1MinY - road2MaxY), Math.Abs(road2MinY - road1MaxY));
            return gap <= MergeThreshold;
        }
    }



    /// <summary>
    /// 合并道路组（修复版：避免使用平均值造成凸起）
    /// </summary>
    private RoadSegment MergeRoadGroup(List<RoadSegment> group, bool isHorizontal)
    {
        if (isHorizontal)
        {
            // 使用最常见的Y坐标，而不是平均值
            int bestY = FindMostCommonCoordinate(group.Select(r => r.StartPoint.Y));
            
            int minX = group.Min(r => Math.Min(r.StartPoint.X, r.EndPoint.X));
            int maxX = group.Max(r => Math.Max(r.StartPoint.X, r.EndPoint.X));

            return new RoadSegment
            {
                StartPoint = new Vector2I(minX, bestY),
                EndPoint = new Vector2I(maxX, bestY),
                Width = group.Max(r => r.Width),
                Type = group.First().Type
            };
        }
        else
        {
            // 使用最常见的X坐标，而不是平均值
            int bestX = FindMostCommonCoordinate(group.Select(r => r.StartPoint.X));
            int minY = group.Min(r => Math.Min(r.StartPoint.Y, r.EndPoint.Y));
            int maxY = group.Max(r => Math.Max(r.StartPoint.Y, r.EndPoint.Y));

            return new RoadSegment
            {
                StartPoint = new Vector2I(bestX, minY),
                EndPoint = new Vector2I(bestX, maxY),
                Width = group.Max(r => r.Width),
                Type = group.First().Type
            };
        }
    }

    /// <summary>
    /// 找到最常见的坐标值，避免平均值造成的偏移
    /// </summary>
    private int FindMostCommonCoordinate(IEnumerable<int> coordinates)
    {
        var coordList = coordinates.ToList();

        // 如果只有一个坐标，直接返回
        if (coordList.Count == 1)
            return coordList[0];

        // 找到出现频率最高的坐标
        var frequencyMap = coordList.GroupBy(x => x)
                                   .ToDictionary(g => g.Key, g => g.Count());

        var mostCommon = frequencyMap.OrderByDescending(kvp => kvp.Value).First();

        // 如果有多个坐标出现频率相同，选择中位数
        if (frequencyMap.Values.Count(v => v == mostCommon.Value) > 1)
        {
            var sortedCoords = coordList.OrderBy(x => x).ToList();
            return sortedCoords[sortedCoords.Count / 2]; // 中位数
        }

        return mostCommon.Key;
    }

    /// <summary>
    /// 检查指定坐标是否在道路上（核心查询方法）
    /// </summary>
    public bool IsOnRoad(Vector2I coordinate)
    {
        // 1. 检查缓存
        if (queryCache.TryGetValue(coordinate, out bool cachedResult))
        {
            return cachedResult;
        }

        // 2. 获取附近的道路段
        var nearbySegments = spatialIndex.GetNearbySegments(coordinate);

        // 3. 检查是否在任何道路段上（包含拐角处理）
        bool result = false;
        foreach (var segment in nearbySegments)
        {
            if (IsPointOnRoadSegmentWithCorners(coordinate, segment))
            {
                result = true;
                break;
            }
        }

        // 4. 缓存结果
        CacheResult(coordinate, result);

        return result;
    }

    /// <summary>
    /// 检查指定坐标是否在主路上（带缓存）
    /// </summary>
    public bool IsOnMainRoad(Vector2I coordinate)
    {
        // 1. 检查缓存
        if (mainRoadQueryCache.TryGetValue(coordinate, out bool cachedResult))
        {
            return cachedResult;
        }

        // 2. 获取附近的主路段
        var nearbySegments = mainRoadSpatialIndex.GetNearbySegments(coordinate);

        // 3. 检查是否在任何主路段上
        bool result = false;
        foreach (var segment in nearbySegments)
        {
            if (IsPointOnRoadSegmentWithCorners(coordinate, segment))
            {
                result = true;
                break;
            }
        }

        // 4. 缓存结果
        CacheMainRoadResult(coordinate, result);

        return result;
    }

    /// <summary>
    /// 检查指定坐标是否在小路上（带缓存）
    /// </summary>
    public bool IsOnLocalRoad(Vector2I coordinate)
    {
        // 1. 检查缓存
        if (localRoadQueryCache.TryGetValue(coordinate, out bool cachedResult))
        {
            return cachedResult;
        }

        // 2. 获取附近的小路段
        var nearbySegments = localRoadSpatialIndex.GetNearbySegments(coordinate);

        // 3. 检查是否在任何小路段上
        bool result = false;
        foreach (var segment in nearbySegments)
        {
            if (IsPointOnRoadSegmentWithCorners(coordinate, segment))
            {
                result = true;
                break;
            }
        }

        // 4. 缓存结果
        CacheLocalRoadResult(coordinate, result);

        return result;
    }

    /// <summary>
    /// 获取坐标所在的道路类型
    /// </summary>
    public RoadType? GetRoadTypeAtCoordinate(Vector2I coordinate)
    {
        if (IsOnMainRoad(coordinate)) return RoadType.MainRoad;
        if (IsOnLocalRoad(coordinate)) return RoadType.LocalRoad;
        return null; // 不在任何道路上
    }

    /// <summary>
    /// 检查点是否在道路段上（改进版，更好地处理拐角）
    /// </summary>
    private bool IsPointOnRoadSegment(Vector2I point, RoadSegment segment)
    {
        // 快速边界框检查
        if (point.X < segment.MinX || point.X > segment.MaxX ||
            point.Y < segment.MinY || point.Y > segment.MaxY)
        {
            return false;
        }

        // 精确检查
        if (segment.IsHorizontal)
        {
            // 水平道路：检查Y距离和X范围
            int distanceY = Math.Abs(point.Y - segment.StartPoint.Y);
            if (distanceY > segment.Width / 2) return false;

            int minX = Math.Min(segment.StartPoint.X, segment.EndPoint.X);
            int maxX = Math.Max(segment.StartPoint.X, segment.EndPoint.X);
            return point.X >= minX && point.X <= maxX;
        }
        else if (segment.IsVertical)
        {
            // 垂直道路：检查X距离和Y范围
            int distanceX = Math.Abs(point.X - segment.StartPoint.X);
            if (distanceX > segment.Width / 2) return false;

            int minY = Math.Min(segment.StartPoint.Y, segment.EndPoint.Y);
            int maxY = Math.Max(segment.StartPoint.Y, segment.EndPoint.Y);
            return point.Y >= minY && point.Y <= maxY;
        }

        return false;
    }

    /// <summary>
    /// 检查点是否在道路段上（包含拐角处理，精确版）
    /// </summary>
    private bool IsPointOnRoadSegmentWithCorners(Vector2I point, RoadSegment segment)
    {
        // 先用标准方法检查
        if (IsPointOnRoadSegment(point, segment))
        {
            return true;
        }

        // 拐角处理：只在端点处稍微扩展
        int cornerRadius = segment.Width / 2; // 保持和道路宽度一致

        // 检查起点附近（只有在端点处才扩展）
        int distToStart = Math.Max(Math.Abs(point.X - segment.StartPoint.X), Math.Abs(point.Y - segment.StartPoint.Y));
        if (distToStart <= cornerRadius)
        {
            return true;
        }

        // 检查终点附近（只有在端点处才扩展）
        int distToEnd = Math.Max(Math.Abs(point.X - segment.EndPoint.X), Math.Abs(point.Y - segment.EndPoint.Y));
        if (distToEnd <= cornerRadius)
        {
            return true;
        }

        return false;
    }

    /// <summary>
    /// 检查点是否在拐角区域内
    /// </summary>
    private bool IsPointInCornerArea(Vector2I point)
    {
        // 查找所有可能的拐角点
        var cornerPoints = FindAllCornerPoints();

        foreach (var corner in cornerPoints)
        {
            // 检查点是否在拐角的影响范围内
            int distance = Math.Max(Math.Abs(point.X - corner.X), Math.Abs(point.Y - corner.Y));
            if (distance <= DefaultRoadWidth / 2)
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// 查找所有拐角点
    /// </summary>
    private List<Vector2I> FindAllCornerPoints()
    {
        var corners = new HashSet<Vector2I>();

        // 查找水平和垂直道路的交叉点
        var horizontalRoads = allRoadSegments.Where(s => s.IsHorizontal).ToList();
        var verticalRoads = allRoadSegments.Where(s => s.IsVertical).ToList();

        foreach (var hRoad in horizontalRoads)
        {
            foreach (var vRoad in verticalRoads)
            {
                var intersection = FindSegmentIntersection(hRoad, vRoad);
                if (intersection.HasValue)
                {
                    corners.Add(intersection.Value);
                }
            }
        }

        return corners.ToList();
    }

    /// <summary>
    /// 查找两个道路段的交叉点
    /// </summary>
    private Vector2I? FindSegmentIntersection(RoadSegment hRoad, RoadSegment vRoad)
    {
        if (!hRoad.IsHorizontal || !vRoad.IsVertical) return null;

        int hY = hRoad.StartPoint.Y;
        int vX = vRoad.StartPoint.X;

        int hMinX = Math.Min(hRoad.StartPoint.X, hRoad.EndPoint.X);
        int hMaxX = Math.Max(hRoad.StartPoint.X, hRoad.EndPoint.X);
        int vMinY = Math.Min(vRoad.StartPoint.Y, vRoad.EndPoint.Y);
        int vMaxY = Math.Max(vRoad.StartPoint.Y, vRoad.EndPoint.Y);

        if (vX >= hMinX && vX <= hMaxX && hY >= vMinY && hY <= vMaxY)
        {
            return new Vector2I(vX, hY);
        }

        return null;
    }

    /// <summary>
    /// 缓存查询结果
    /// </summary>
    private void CacheResult(Vector2I coordinate, bool result)
    {
        if (queryCache.Count >= maxCacheSize)
        {
            // 简单的缓存清理：清除一半
            var keysToRemove = queryCache.Keys.Take(maxCacheSize / 2).ToList();
            foreach (var key in keysToRemove)
            {
                queryCache.Remove(key);
            }
        }

        queryCache[coordinate] = result;
    }

    /// <summary>
    /// 缓存主路查询结果
    /// </summary>
    private void CacheMainRoadResult(Vector2I coordinate, bool result)
    {
        if (mainRoadQueryCache.Count >= maxCacheSize)
        {
            // 简单的缓存清理：清除一半
            var keysToRemove = mainRoadQueryCache.Keys.Take(maxCacheSize / 2).ToList();
            foreach (var key in keysToRemove)
            {
                mainRoadQueryCache.Remove(key);
            }
        }

        mainRoadQueryCache[coordinate] = result;
    }

    /// <summary>
    /// 缓存小路查询结果
    /// </summary>
    private void CacheLocalRoadResult(Vector2I coordinate, bool result)
    {
        if (localRoadQueryCache.Count >= maxCacheSize)
        {
            // 简单的缓存清理：清除一半
            var keysToRemove = localRoadQueryCache.Keys.Take(maxCacheSize / 2).ToList();
            foreach (var key in keysToRemove)
            {
                localRoadQueryCache.Remove(key);
            }
        }

        localRoadQueryCache[coordinate] = result;
    }

    /// <summary>
    /// 获取所有道路线段
    /// </summary>
    public List<RoadSegment> GetAllRoadSegments()
    {
        return new List<RoadSegment>(allRoadSegments);
    }

    /// <summary>
    /// 获取主路线段
    /// </summary>
    public List<RoadSegment> GetMainRoadSegments()
    {
        return new List<RoadSegment>(mainRoadSegments);
    }

    /// <summary>
    /// 获取小路线段
    /// </summary>
    public List<RoadSegment> GetLocalRoadSegments()
    {
        return new List<RoadSegment>(localRoadSegments);
    }

    /// <summary>
    /// 获取指定类型的道路线段
    /// </summary>
    public List<RoadSegment> GetRoadSegmentsByType(RoadType roadType)
    {
        switch (roadType)
        {
            case RoadType.MainRoad:
                return GetMainRoadSegments();
            case RoadType.LocalRoad:
                return GetLocalRoadSegments();
            case RoadType.Highway:
                return allRoadSegments.Where(r => r.Type == RoadType.Highway).ToList();
            default:
                return new List<RoadSegment>();
        }
    }

    /// <summary>
    /// 获取统计信息
    /// </summary>
    public string GetStats()
    {
        return $"道路系统统计 - 总计: {allRoadSegments.Count} (主路: {mainRoadSegments.Count}, 小路: {localRoadSegments.Count}), " +
               $"缓存: 统一{queryCache.Count}, 主路{mainRoadQueryCache.Count}, 小路{localRoadQueryCache.Count} (最大: {maxCacheSize})";
    }

    /// <summary>
    /// 清除缓存（当道路发生变化时调用）
    /// </summary>
    public void ClearCache()
    {
        queryCache.Clear();
        mainRoadQueryCache.Clear();
        localRoadQueryCache.Clear();
    }

    /// <summary>
    /// 清除指定类型的道路
    /// </summary>
    public void ClearRoadsByType(RoadType roadType)
    {
        if (roadType == RoadType.LocalRoad)
        {
            // 清除小路
            localRoadSegments.Clear();
            localRoadSpatialIndex.Clear();
            localRoadQueryCache.Clear();

            // 从统一存储中移除小路
            allRoadSegments.RemoveAll(r => r.Type == RoadType.LocalRoad);

            // 重建统一空间索引
            RebuildUnifiedSpatialIndex();
        }
        else if (roadType == RoadType.MainRoad)
        {
            // 清除主路
            mainRoadSegments.Clear();
            mainRoadSpatialIndex.Clear();
            mainRoadQueryCache.Clear();

            // 从统一存储中移除主路
            allRoadSegments.RemoveAll(r => r.Type == RoadType.MainRoad);

            // 重建统一空间索引
            RebuildUnifiedSpatialIndex();
        }

        // 清除缓存
        ClearCache();

        GD.Print($"已清除 {roadType} 类型的道路");
    }

    /// <summary>
    /// 重建统一空间索引
    /// </summary>
    private void RebuildUnifiedSpatialIndex()
    {
        spatialIndex.Clear();
        foreach (var segment in allRoadSegments)
        {
            spatialIndex.AddRoadSegment(segment);
        }
    }

    /// <summary>
    /// 添加单个道路段到系统中（用于动态添加小路等）
    /// </summary>
    public void AddRoadSegment(RoadSegment roadSegment)
    {
        if (roadSegment == null) return;

        // 计算边界
        roadSegment.CalculateBounds();

        // 根据道路类型添加到相应的存储
        if (roadSegment.Type == RoadType.LocalRoad)
        {
            // 添加到小路存储
            localRoadSegments.Add(roadSegment);
            localRoadSpatialIndex.AddRoadSegment(roadSegment);
        }
        else
        {
            // 添加到主路存储
            mainRoadSegments.Add(roadSegment);
            mainRoadSpatialIndex.AddRoadSegment(roadSegment);
        }

        // 同时添加到统一存储（兼容性）
        allRoadSegments.Add(roadSegment);
        spatialIndex.AddRoadSegment(roadSegment);

        // 清除缓存，因为道路系统已经改变
        ClearCache();

        GD.Print($"添加道路段: {roadSegment.StartPoint} -> {roadSegment.EndPoint} (类型: {roadSegment.Type})");
    }

    /// <summary>
    /// 检查道路连通性
    /// </summary>
    private void CheckRoadConnectivity()
    {
        var horizontalRoads = allRoadSegments.Where(s => s.IsHorizontal).ToList();
        var verticalRoads = allRoadSegments.Where(s => s.IsVertical).ToList();

        GD.Print($"道路连通性检查:");
        GD.Print($"  水平道路: {horizontalRoads.Count}条");
        GD.Print($"  垂直道路: {verticalRoads.Count}条");

        // 检查是否有孤立的道路段
        int isolatedCount = 0;
        foreach (var road in allRoadSegments)
        {
            bool hasConnection = false;
            foreach (var otherRoad in allRoadSegments)
            {
                if (road == otherRoad) continue;

                // 检查是否有连接点
                if (RoadsAreConnected(road, otherRoad))
                {
                    hasConnection = true;
                    break;
                }
            }

            if (!hasConnection)
            {
                isolatedCount++;
                GD.Print($"  发现孤立道路: {road.StartPoint} -> {road.EndPoint}");
            }
        }

        if (isolatedCount == 0)
        {
            GD.Print("  ✓ 所有道路都有连接");
        }
        else
        {
            GD.Print($"  ⚠ 发现 {isolatedCount} 条孤立道路");
        }
    }

    /// <summary>
    /// 检查两条道路是否连接
    /// </summary>
    private bool RoadsAreConnected(RoadSegment road1, RoadSegment road2)
    {
        // 检查端点是否重合或相近
        var points1 = new[] { road1.StartPoint, road1.EndPoint };
        var points2 = new[] { road2.StartPoint, road2.EndPoint };

        foreach (var p1 in points1)
        {
            foreach (var p2 in points2)
            {
                int distance = Math.Max(Math.Abs(p1.X - p2.X), Math.Abs(p1.Y - p2.Y));
                if (distance <= DefaultRoadWidth)
                {
                    return true;
                }
            }
        }

        return false;
    }

    /// <summary>
    /// 处理断头路：将端点不在中心点的线段缩短到与其他线段的交点
    /// </summary>
    private void TrimDeadEndRoads(HashSet<Vector2I> centerPoints)
    {
        GD.Print("   开始处理断头路...");
        GD.Print($"   中心点数量: {centerPoints.Count}");
        GD.Print($"   道路段数量: {allRoadSegments.Count}");

        // 调试：输出前几个中心点
        var centerPointsList = centerPoints.Take(5).ToList();
        GD.Print($"   前5个中心点: {string.Join(", ", centerPointsList.Select(p => $"({p.X},{p.Y})"))}");

        var modifiedSegments = new List<RoadSegment>();
        int trimmedCount = 0;
        int candidateCount = 0; // 候选断头路数量

        foreach (var segment in allRoadSegments.ToList())
        {
            bool startIsCenter = centerPoints.Contains(segment.StartPoint);
            bool endIsCenter = centerPoints.Contains(segment.EndPoint);

            GD.Print($"   检查线段: {segment.StartPoint}->{segment.EndPoint}, 起点是中心:{startIsCenter}, 终点是中心:{endIsCenter}");

            // 如果两个端点都是中心点，或者都不是中心点，则不需要处理
            if (startIsCenter == endIsCenter)
            {
                if (!startIsCenter && !endIsCenter)
                {
                    GD.Print($"     跳过: 两端都不是中心点");
                }
                else if (startIsCenter && endIsCenter)
                {
                    GD.Print($"     跳过: 两端都是中心点");
                }
                continue;
            }

            candidateCount++;
            GD.Print($"     发现候选断头路: {segment.StartPoint}->{segment.EndPoint}");

            // 找到需要修剪的端点（不是中心点的那个）
            Vector2I deadEndPoint = startIsCenter ? segment.EndPoint : segment.StartPoint;
            Vector2I centerPoint = startIsCenter ? segment.StartPoint : segment.EndPoint;

            GD.Print($"     中心点: {centerPoint}, 断头点: {deadEndPoint}");

            // 查找这条线段与其他线段的交点
            var intersectionPoint = FindNearestIntersection(segment, deadEndPoint, centerPoint);

            if (intersectionPoint.HasValue)
            {
                GD.Print($"     找到交点: {intersectionPoint.Value}");

                // 创建修剪后的线段
                var trimmedSegment = new RoadSegment
                {
                    StartPoint = centerPoint,
                    EndPoint = intersectionPoint.Value,
                    Width = segment.Width,
                    Type = segment.Type
                };
                trimmedSegment.CalculateBounds();

                // 替换原线段
                allRoadSegments.Remove(segment);
                allRoadSegments.Add(trimmedSegment);

                // 根据类型更新对应的列表
                if (segment.Type == RoadType.MainRoad)
                {
                    mainRoadSegments.Remove(segment);
                    mainRoadSegments.Add(trimmedSegment);
                }
                else
                {
                    localRoadSegments.Remove(segment);
                    localRoadSegments.Add(trimmedSegment);
                }

                trimmedCount++;
                GD.Print($"     ✅ 修剪断头路: {segment.StartPoint}->{segment.EndPoint} -> {trimmedSegment.StartPoint}->{trimmedSegment.EndPoint}");
            }
            else
            {
                GD.Print($"     ❌ 未找到交点，保持原样");
            }
        }

        GD.Print($"   断头路处理完成: 候选数量 {candidateCount}, 实际修剪 {trimmedCount} 条线段");

        // 如果有修剪操作，重建空间索引
        if (trimmedCount > 0)
        {
            RebuildAllSpatialIndexes();
            GD.Print("   重建空间索引完成");
        }
    }

    /// <summary>
    /// 重建所有空间索引
    /// </summary>
    private void RebuildAllSpatialIndexes()
    {
        // 清空所有索引
        spatialIndex.Clear();
        mainRoadSpatialIndex.Clear();
        localRoadSpatialIndex.Clear();

        // 重建统一索引
        foreach (var segment in allRoadSegments)
        {
            spatialIndex.AddRoadSegment(segment);
        }

        // 重建主路索引
        foreach (var segment in mainRoadSegments)
        {
            mainRoadSpatialIndex.AddRoadSegment(segment);
        }

        // 重建小路索引
        foreach (var segment in localRoadSegments)
        {
            localRoadSpatialIndex.AddRoadSegment(segment);
        }

        // 清除缓存
        ClearCache();
    }

    /// <summary>
    /// 查找线段与其他线段的最近交点
    /// </summary>
    private Vector2I? FindNearestIntersection(RoadSegment targetSegment, Vector2I deadEndPoint, Vector2I centerPoint)
    {
        Vector2I? nearestIntersection = null;
        float nearestDistance = float.MaxValue;
        int checkedSegments = 0;
        int foundIntersections = 0;

        GD.Print($"       查找交点: 目标线段 {targetSegment.StartPoint}->{targetSegment.EndPoint}");

        foreach (var otherSegment in allRoadSegments)
        {
            if (otherSegment == targetSegment)
                continue;

            checkedSegments++;

            // 计算两条线段的交点
            var intersection = CalculateSegmentIntersection(targetSegment, otherSegment);

            if (intersection.HasValue)
            {
                foundIntersections++;
                Vector2I intersectionPoint = intersection.Value;

                GD.Print($"         与线段 {otherSegment.StartPoint}->{otherSegment.EndPoint} 的交点: {intersectionPoint}");

                // 检查交点是否在目标线段上（从中心点到断头点之间）
                if (IsPointOnSegment(intersectionPoint, centerPoint, deadEndPoint))
                {
                    // 计算交点到中心点的距离
                    float distance = centerPoint.DistanceTo(intersectionPoint);

                    GD.Print($"         交点在线段上，距离中心点: {distance:F1}");

                    if (distance < nearestDistance && distance > 1.0f) // 避免过于接近中心点
                    {
                        nearestDistance = distance;
                        nearestIntersection = intersectionPoint;
                        GD.Print($"         ✅ 更新最近交点: {intersectionPoint}, 距离: {distance:F1}");
                    }
                    else
                    {
                        GD.Print($"         ❌ 距离不符合条件: {distance:F1} (需要 > 1.0 且 < {nearestDistance:F1})");
                    }
                }
                else
                {
                    GD.Print($"         ❌ 交点不在目标线段上");
                }
            }
        }

        GD.Print($"       检查了 {checkedSegments} 个线段，找到 {foundIntersections} 个交点，最终选择: {nearestIntersection}");
        return nearestIntersection;
    }

    /// <summary>
    /// 计算两条线段的交点
    /// </summary>
    private Vector2I? CalculateSegmentIntersection(RoadSegment seg1, RoadSegment seg2)
    {
        // 只处理垂直相交的情况（水平线与垂直线）
        if (seg1.IsHorizontal == seg2.IsHorizontal)
            return null;

        RoadSegment horizontalSeg = seg1.IsHorizontal ? seg1 : seg2;
        RoadSegment verticalSeg = seg1.IsHorizontal ? seg2 : seg1;

        // 计算交点坐标
        int intersectionX = verticalSeg.StartPoint.X; // 垂直线的X坐标
        int intersectionY = horizontalSeg.StartPoint.Y; // 水平线的Y坐标

        // 检查交点是否在两条线段的范围内
        bool xInHorizontalRange = intersectionX >= Math.Min(horizontalSeg.StartPoint.X, horizontalSeg.EndPoint.X) &&
                                  intersectionX <= Math.Max(horizontalSeg.StartPoint.X, horizontalSeg.EndPoint.X);
        bool yInVerticalRange = intersectionY >= Math.Min(verticalSeg.StartPoint.Y, verticalSeg.EndPoint.Y) &&
                                intersectionY <= Math.Max(verticalSeg.StartPoint.Y, verticalSeg.EndPoint.Y);

        if (xInHorizontalRange && yInVerticalRange)
        {
            return new Vector2I(intersectionX, intersectionY);
        }

        return null;
    }

    /// <summary>
    /// 检查点是否在线段上
    /// </summary>
    private bool IsPointOnSegment(Vector2I point, Vector2I segStart, Vector2I segEnd)
    {
        // 检查点是否在线段的边界框内
        int minX = Math.Min(segStart.X, segEnd.X);
        int maxX = Math.Max(segStart.X, segEnd.X);
        int minY = Math.Min(segStart.Y, segEnd.Y);
        int maxY = Math.Max(segStart.Y, segEnd.Y);

        if (point.X < minX || point.X > maxX || point.Y < minY || point.Y > maxY)
            return false;

        // 对于水平或垂直线段，检查是否在直线上
        if (segStart.X == segEnd.X) // 垂直线段
        {
            return point.X == segStart.X;
        }
        else if (segStart.Y == segEnd.Y) // 水平线段
        {
            return point.Y == segStart.Y;
        }

        return false;
    }
}
