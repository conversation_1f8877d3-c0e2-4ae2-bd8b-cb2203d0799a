using Godot;
using System;

/// <summary>
/// 地图显示UI控制器
/// 管理地图显示窗口和相关控件
/// </summary>
public partial class MapDisplayUI : Control
{
    [Export] public PackedScene MapDisplayScene { get; set; }
    
    private MapDisplay mapDisplay;
    private Panel mapPanel;
    private VBoxContainer controlPanel;
    private Button refreshButton;
    private Button toggleButton;
    private Label infoLabel;
    private Map_res mapRes;
    
    private bool isVisible = true;

    public override void _Ready()
    {
        // 设置为全屏
        SetAnchorsAndOffsetsPreset(Control.LayoutPreset.FullRect);
        CreateUI();
    }

    /// <summary>
    /// 创建UI界面
    /// </summary>
    private void CreateUI()
    {
        // 创建主面板
        mapPanel = new Panel();
        mapPanel.SetAnchorsAndOffsetsPreset(Control.LayoutPreset.TopRight);
        mapPanel.Position = new Vector2(-420, 10);
        mapPanel.Size = new Vector2(410, 350);
        mapPanel.AddThemeStyleboxOverride("panel", CreatePanelStyle());
        AddChild(mapPanel);

        // 创建地图显示控件
        mapDisplay = new MapDisplay();
        mapDisplay.MapSize = new Vector2(400, 300);
        mapDisplay.Position = new Vector2(5, 5);
        mapPanel.AddChild(mapDisplay);

        // 创建控制面板
        CreateControlPanel();

        // 创建信息标签
        CreateInfoLabel();
    }

    /// <summary>
    /// 创建控制面板
    /// </summary>
    private void CreateControlPanel()
    {
        controlPanel = new VBoxContainer();
        controlPanel.Position = new Vector2(5, 310);
        controlPanel.Size = new Vector2(400, 35);
        mapPanel.AddChild(controlPanel);

        // 创建按钮容器
        var buttonContainer = new HBoxContainer();
        controlPanel.AddChild(buttonContainer);

        // 刷新按钮
        refreshButton = new Button();
        refreshButton.Text = "刷新地图";
        refreshButton.Size = new Vector2(80, 30);
        refreshButton.Pressed += OnRefreshPressed;
        buttonContainer.AddChild(refreshButton);

        // 切换显示按钮
        toggleButton = new Button();
        toggleButton.Text = "隐藏";
        toggleButton.Size = new Vector2(60, 30);
        toggleButton.Pressed += OnTogglePressed;
        buttonContainer.AddChild(toggleButton);

        // 添加间隔
        var spacer = new Control();
        spacer.SizeFlagsHorizontal = Control.SizeFlags.ExpandFill;
        buttonContainer.AddChild(spacer);
    }

    /// <summary>
    /// 创建信息标签
    /// </summary>
    private void CreateInfoLabel()
    {
        infoLabel = new Label();
        infoLabel.Text = "地图未加载";
        infoLabel.AddThemeColorOverride("font_color", Colors.White);
        infoLabel.AddThemeStyleboxOverride("normal", CreateLabelStyle());
        infoLabel.Position = new Vector2(-200, -25);
        infoLabel.Size = new Vector2(190, 20);
        infoLabel.SetAnchorsAndOffsetsPreset(Control.LayoutPreset.BottomRight);
        AddChild(infoLabel);
    }

    /// <summary>
    /// 创建面板样式
    /// </summary>
    private StyleBoxFlat CreatePanelStyle()
    {
        var style = new StyleBoxFlat();
        style.BgColor = new Color(0.2f, 0.2f, 0.2f, 0.9f);
        style.BorderColor = Colors.Gray;
        style.BorderWidthTop = 2;
        style.BorderWidthBottom = 2;
        style.BorderWidthLeft = 2;
        style.BorderWidthRight = 2;
        style.CornerRadiusTopLeft = 5;
        style.CornerRadiusTopRight = 5;
        style.CornerRadiusBottomLeft = 5;
        style.CornerRadiusBottomRight = 5;
        return style;
    }

    /// <summary>
    /// 创建标签样式
    /// </summary>
    private StyleBoxFlat CreateLabelStyle()
    {
        var style = new StyleBoxFlat();
        style.BgColor = new Color(0.1f, 0.1f, 0.1f, 0.8f);
        style.CornerRadiusTopLeft = 3;
        style.CornerRadiusTopRight = 3;
        style.CornerRadiusBottomLeft = 3;
        style.CornerRadiusBottomRight = 3;
        return style;
    }

    /// <summary>
    /// 设置地图数据
    /// </summary>
    public void SetMapData(Map_res mapRes)
    {
        this.mapRes = mapRes;
        
        if (mapDisplay != null)
        { 
            UpdateInfoLabel();
        }
    }

    /// <summary>
    /// 更新信息标签
    /// </summary>
    private void UpdateInfoLabel()
    {
        if (mapRes == null)
        {
            infoLabel.Text = "地图未加载";
            return;
        }
        mapDisplay.SetMapData(mapRes);
        int mainRoadCount = mapRes.roadSystem?.GetMainRoadSegments().Count ?? 0;
        int localRoadCount = mapRes.roadSystem?.GetLocalRoadSegments().Count ?? 0;
        int totalRoadCount = mapRes.roadSystem?.GetAllRoadSegments().Count ?? 0;

        string info = $"区域: {mapRes.reg_list.Count}个 | " +
                     $"尺寸: {Math.Abs(mapRes.EdgeRight - mapRes.EdgeLeft)}×{Math.Abs(mapRes.EdgeBottom - mapRes.EdgeTop)} | " +
                     $"道路: {totalRoadCount}条 (主路: {mainRoadCount}, 小路: {localRoadCount})";
        
        infoLabel.Text = info;
    }

    /// <summary>
    /// 刷新按钮点击事件
    /// </summary>
    private void OnRefreshPressed()
    {
        if (mapDisplay != null)
        {
            mapDisplay.RefreshMap();
            UpdateInfoLabel();
            GD.Print("地图已刷新");
        }
    }

    /// <summary>
    /// 切换显示按钮点击事件
    /// </summary>
    private void OnTogglePressed()
    {
        isVisible = !isVisible;
        mapPanel.Visible = isVisible;
        toggleButton.Text = isVisible ? "隐藏" : "显示";
        
        if (isVisible)
        {
            toggleButton.Position = new Vector2(-70, -35);
        }
        else
        {
            toggleButton.Position = new Vector2(-70, 10);
        }
    }

    /// <summary>
    /// 处理输入事件
    /// </summary>
    public override void _Input(InputEvent @event)
    {
        // 按M键切换地图显示
        if (@event is InputEventKey keyEvent && keyEvent.Pressed)
        {
            if (keyEvent.Keycode == Key.M)
            {
                OnTogglePressed();
            }
            else if (keyEvent.Keycode == Key.F5)
            {
                OnRefreshPressed();
            }
        }
    }

    /// <summary>
    /// 获取地图显示控件
    /// </summary>
    public MapDisplay GetMapDisplay()
    {
        return mapDisplay;
    }

    /// <summary>
    /// 设置地图显示尺寸
    /// </summary>
    public void SetMapDisplaySize(Vector2 size)
    {
        if (mapDisplay != null)
        {
            mapDisplay.SetDisplaySize(size);
            
            // 调整面板尺寸
            mapPanel.Size = new Vector2(size.X + 10, size.Y + 50);
            controlPanel.Position = new Vector2(5, size.Y + 10);
        }
    }
}
