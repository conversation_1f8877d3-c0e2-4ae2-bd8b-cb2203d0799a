using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// 技能范围显示器 - 使用Polygon2D显示连续的技能范围多边形
/// </summary>
public partial class SkillRangeDisplay : Node2D
{
    // TileMapLayer引用，用于获取网格信息
    private TileMapLayer tileMapLayer;
    
    // 当前显示的多边形节点
    private Polygon2D currentPolygon;
    
    // 网格大小（像素）
    private Vector2 tileSize = new Vector2(32, 32);
    
    // 显示配置
    public Color RangeColor { get; set; } = new Color(1, 0, 0, 0.3f); // 半透明红色
    public Color BorderColor { get; set; } = new Color(1, 0, 0, 0.8f); // 边框颜色
    public float BorderWidth { get; set; } = 2.0f;
    public bool ShowBorder { get; set; } = true;

    /// <summary>
    /// 初始化技能范围显示器
    /// </summary>
    /// <param name="tileMapLayer">用于获取网格信息的TileMapLayer</param>
    public void Initialize(TileMapLayer tileMapLayer)
    {
        this.tileMapLayer = tileMapLayer;
        
        if (tileMapLayer?.TileSet != null)
        {
            // 获取瓦片大小
            tileSize = tileMapLayer.TileSet.TileSize;
            GD.Print($"技能范围显示器初始化完成，网格大小: {tileSize}");
        }
        else
        {
            GD.PrintErr("TileMapLayer或TileSet为空，使用默认网格大小");
        }
    }

    /// <summary>
    /// 显示正方形技能范围
    /// </summary>
    /// <param name="centerCoord">中心坐标（网格坐标）</param>
    /// <param name="sideLength">正方形边长（网格单位）</param>
    public void ShowSquareRange(Vector2I centerCoord, int sideLength)
    {
        // 清除当前显示
        ClearRange();

        // 计算正方形的网格坐标范围
        var affectedCells = CalculateSquareCells(centerCoord, sideLength);

        // 生成多边形并显示
        var polygonPoints = GenerateSquarePolygon(centerCoord, sideLength);
        ShowPolygon(polygonPoints);

        GD.Print($"显示正方形范围：中心({centerCoord.X}, {centerCoord.Y})，边长{sideLength}");
    }



    /// <summary>
    /// 显示距离范围（从中心点开始一定距离内的所有格子）
    /// </summary>
    /// <param name="centerCoord">中心位置（网格坐标）</param>
    /// <param name="distance">距离范围（网格单位）</param>
    /// <param name="useGridDistance">是否使用网格距离（曼哈顿距离），false则使用欧几里得距离</param>
    public void ShowDistanceRange(Vector2I centerCoord, int distance, bool useGridDistance = true)
    {
        // 清除当前显示
        ClearRange();

        // 计算距离范围内的所有格子
        var rangeCells = CalculateDistanceCells(centerCoord, distance, useGridDistance);

        if (useGridDistance)
        {
            // 网格距离：可以使用连续多边形
            var polygonPoints = GenerateComplexPolygon(rangeCells);
            ShowPolygon(polygonPoints);
        }
        else
        {
            // 直线距离：生成沿格子边缘的整体边界
            var polygonPoints = GenerateGridAlignedBoundary(rangeCells);
            ShowPolygon(polygonPoints);
        }

        string distanceType = useGridDistance ? "网格距离" : "直线距离";
        GD.Print($"显示距离范围：中心({centerCoord.X}, {centerCoord.Y})，{distanceType}{distance}，包含{rangeCells.Count}个格子");
    }

    /// <summary>
    /// 显示扇形范围（以方格为单位）
    /// </summary>
    /// <param name="centerCoord">中心位置（网格坐标）</param>
    /// <param name="range">扇形半径（网格单位）</param>
    /// <param name="direction">扇形朝向（度数，0度为向右，90度为向上）</param>
    /// <param name="angle">扇形角度（度数，如90度表示四分之一圆）</param>
    /// <param name="useGridDistance">是否使用网格距离</param>
    public void ShowSectorRange(Vector2I centerCoord, int range, float direction, float angle, bool useGridDistance = true)
    {
        // 清除当前显示
        ClearRange();

        // 计算扇形范围内的所有格子
        var sectorCells = CalculateSectorCells(centerCoord, range, direction, angle, useGridDistance);

        // 生成多边形并显示
        var polygonPoints = GenerateGridAlignedBoundary(sectorCells);
        ShowPolygon(polygonPoints);

        string distanceType = useGridDistance ? "网格距离" : "直线距离";
        GD.Print($"显示扇形范围：中心({centerCoord.X}, {centerCoord.Y})，{distanceType}{range}，方向{direction}°，角度{angle}°，包含{sectorCells.Count}个格子");
    }

    /// <summary>
    /// 显示视野范围（考虑障碍物遮挡的复杂形状）
    /// </summary>
    /// <param name="centerCoord">观察者位置（网格坐标）</param>
    /// <param name="viewRadius">视野半径（网格单位）</param>
    /// <param name="obstacleChecker">障碍物检查函数</param>
    public void ShowViewRange(Vector2I centerCoord, int viewRadius, Func<Vector2I, bool> obstacleChecker = null)
    {
        // 清除当前显示
        ClearRange();

        // 计算视野范围内的可见格子
        var visibleCells = CalculateViewCells(centerCoord, viewRadius, obstacleChecker);

        // 生成复杂多边形并显示
        var polygonPoints = GenerateComplexPolygon(visibleCells);
        ShowPolygon(polygonPoints);

        GD.Print($"显示视野范围：中心({centerCoord.X}, {centerCoord.Y})，半径{viewRadius}，可见格子{visibleCells.Count}个");
    }

    /// <summary>
    /// 计算正方形范围内的所有网格坐标
    /// </summary>
    private HashSet<Vector2I> CalculateSquareCells(Vector2I centerCoord, int sideLength)
    {
        var cells = new HashSet<Vector2I>();
        int halfSize = sideLength / 2;

        for (int x = centerCoord.X - halfSize; x <= centerCoord.X + halfSize; x++)
        {
            for (int y = centerCoord.Y - halfSize; y <= centerCoord.Y + halfSize; y++)
            {
                cells.Add(new Vector2I(x, y));
            }
        }

        return cells;
    }

    /// <summary>
    /// 计算距离范围内的所有网格坐标
    /// </summary>
    private HashSet<Vector2I> CalculateDistanceCells(Vector2I centerCoord, int distance, bool useGridDistance)
    {
        var cells = new HashSet<Vector2I>();

        // 遍历可能的范围
        for (int x = centerCoord.X - distance; x <= centerCoord.X + distance; x++)
        {
            for (int y = centerCoord.Y - distance; y <= centerCoord.Y + distance; y++)
            {
                var cellCoord = new Vector2I(x, y);

                if (useGridDistance)
                {
                    // 使用曼哈顿距离（网格距离）
                    int gridDistance = Math.Abs(x - centerCoord.X) + Math.Abs(y - centerCoord.Y);
                    if (gridDistance <= distance)
                    {
                        cells.Add(cellCoord);
                    }
                }
                else
                {
                    // 使用欧几里得距离（直线距离）
                    // 计算到格子中心的距离，确保整个格子都在范围内
                    float euclideanDistance = centerCoord.DistanceTo(cellCoord);

                    // 为了确保不会有边界经过格子内部，我们需要检查格子的四个角
                    // 只有当格子的所有角都在范围内时，才包含这个格子
                    bool allCornersInRange = true;
                    Vector2[] corners = {
                        new Vector2(x - 0.5f, y - 0.5f),  // 左上角
                        new Vector2(x + 0.5f, y - 0.5f),  // 右上角
                        new Vector2(x + 0.5f, y + 0.5f),  // 右下角
                        new Vector2(x - 0.5f, y + 0.5f)   // 左下角
                    };

                    foreach (var corner in corners)
                    {
                        float cornerDistance = new Vector2(centerCoord.X, centerCoord.Y).DistanceTo(corner);
                        if (cornerDistance > distance)
                        {
                            allCornersInRange = false;
                            break;
                        }
                    }

                    if (allCornersInRange)
                    {
                        cells.Add(cellCoord);
                    }
                }
            }
        }

        return cells;
    }

    /// <summary>
    /// 计算扇形范围内的所有网格坐标
    /// </summary>
    private HashSet<Vector2I> CalculateSectorCells(Vector2I centerCoord, int range, float direction, float angle, bool useGridDistance)
    {
        var cells = new HashSet<Vector2I>();

        // 将角度转换为弧度
        float directionRad = Mathf.DegToRad(direction);
        float halfAngleRad = Mathf.DegToRad(angle / 2.0f);

        // 计算扇形的起始和结束角度
        float startAngle = directionRad - halfAngleRad;
        float endAngle = directionRad + halfAngleRad;

        // 遍历可能的范围
        for (int x = centerCoord.X - range; x <= centerCoord.X + range; x++)
        {
            for (int y = centerCoord.Y - range; y <= centerCoord.Y + range; y++)
            {
                var cellCoord = new Vector2I(x, y);

                // 跳过中心点
                if (cellCoord == centerCoord)
                {
                    cells.Add(cellCoord);
                    continue;
                }

                // 检查距离
                bool inRange = false;
                if (useGridDistance)
                {
                    int gridDistance = Math.Abs(x - centerCoord.X) + Math.Abs(y - centerCoord.Y);
                    inRange = gridDistance <= range;
                }
                else
                {
                    // 使用格子角点检查（确保不会有边界经过格子内部）
                    bool allCornersInRange = true;
                    Vector2[] corners = {
                        new Vector2(x - 0.5f, y - 0.5f),  // 左上角
                        new Vector2(x + 0.5f, y - 0.5f),  // 右上角
                        new Vector2(x + 0.5f, y + 0.5f),  // 右下角
                        new Vector2(x - 0.5f, y + 0.5f)   // 左下角
                    };

                    foreach (var corner in corners)
                    {
                        float cornerDistance = new Vector2(centerCoord.X, centerCoord.Y).DistanceTo(corner);
                        if (cornerDistance > range)
                        {
                            allCornersInRange = false;
                            break;
                        }
                    }
                    inRange = allCornersInRange;
                }

                if (!inRange)
                    continue;

                // 检查角度（使用格子中心点）
                Vector2 cellCenter = new Vector2(x, y);
                Vector2 centerPos = new Vector2(centerCoord.X, centerCoord.Y);
                Vector2 direction_vec = cellCenter - centerPos;

                // 计算格子相对于中心的角度
                float cellAngle = Mathf.Atan2(direction_vec.Y, direction_vec.X);

                // 处理角度跨越问题（例如从350度到10度）
                if (IsAngleInSector(cellAngle, startAngle, endAngle))
                {
                    cells.Add(cellCoord);
                }
            }
        }

        return cells;
    }

    /// <summary>
    /// 检查角度是否在扇形范围内（处理角度跨越问题）
    /// </summary>
    private bool IsAngleInSector(float angle, float startAngle, float endAngle)
    {
        // 将所有角度标准化到 [0, 2π] 范围
        angle = NormalizeAngle(angle);
        startAngle = NormalizeAngle(startAngle);
        endAngle = NormalizeAngle(endAngle);

        if (startAngle <= endAngle)
        {
            // 正常情况：扇形不跨越0度
            return angle >= startAngle && angle <= endAngle;
        }
        else
        {
            // 跨越情况：扇形跨越0度（例如从350度到10度）
            return angle >= startAngle || angle <= endAngle;
        }
    }

    /// <summary>
    /// 将角度标准化到 [0, 2π] 范围
    /// </summary>
    private float NormalizeAngle(float angle)
    {
        while (angle < 0)
            angle += 2 * Mathf.Pi;
        while (angle >= 2 * Mathf.Pi)
            angle -= 2 * Mathf.Pi;
        return angle;
    }

    /// <summary>
    /// 计算视野范围内的可见格子（考虑障碍物遮挡）
    /// </summary>
    private HashSet<Vector2I> CalculateViewCells(Vector2I centerCoord, int viewRadius, Func<Vector2I, bool> obstacleChecker)
    {
        var visibleCells = new HashSet<Vector2I>();

        // 使用射线投射算法计算视野
        int rayCount = 360; // 360度，每度一条射线

        for (int angle = 0; angle < rayCount; angle++)
        {
            float radians = angle * Mathf.Pi / 180.0f;
            var direction = new Vector2(Mathf.Cos(radians), Mathf.Sin(radians));

            // 沿射线方向投射
            for (float distance = 0; distance <= viewRadius; distance += 0.5f)
            {
                var rayPoint = new Vector2(centerCoord.X, centerCoord.Y) + direction * distance;
                var gridCoord = new Vector2I(Mathf.RoundToInt(rayPoint.X), Mathf.RoundToInt(rayPoint.Y));

                // 检查是否超出范围
                if (centerCoord.DistanceTo(gridCoord) > viewRadius)
                    break;

                // 添加可见格子
                visibleCells.Add(gridCoord);

                // 检查是否遇到障碍物
                if (obstacleChecker != null && obstacleChecker(gridCoord))
                {
                    break; // 遇到障碍物，停止这条射线
                }
            }
        }

        return visibleCells;
    }

    /// <summary>
    /// 生成正方形多边形的顶点
    /// </summary>
    private Vector2[] GenerateSquarePolygon(Vector2I centerCoord, int sideLength)
    {
        // 计算正方形的世界坐标边界
        float halfSize = sideLength * 0.5f;
        Vector2 centerWorld = GridToWorldPosition(centerCoord);
        
        // 计算四个顶点（顺时针）
        Vector2[] vertices = new Vector2[4];
        vertices[0] = centerWorld + new Vector2(-halfSize * tileSize.X, -halfSize * tileSize.Y); // 左上
        vertices[1] = centerWorld + new Vector2(halfSize * tileSize.X, -halfSize * tileSize.Y);  // 右上
        vertices[2] = centerWorld + new Vector2(halfSize * tileSize.X, halfSize * tileSize.Y);   // 右下
        vertices[3] = centerWorld + new Vector2(-halfSize * tileSize.X, halfSize * tileSize.Y);  // 左下
        
        return vertices;
    }



    /// <summary>
    /// 从离散格子生成复杂多边形（沿格子边缘）
    /// </summary>
    private Vector2[] GenerateComplexPolygon(HashSet<Vector2I> cells)
    {
        if (cells.Count == 0)
            return new Vector2[0];

        if (cells.Count == 1)
        {
            // 单个格子，返回格子的四个角
            var cell = cells.First();
            var center = GridToWorldPosition(cell);
            float halfTile = tileSize.X * 0.5f;
            return new Vector2[]
            {
                center + new Vector2(-halfTile, -halfTile),
                center + new Vector2(halfTile, -halfTile),
                center + new Vector2(halfTile, halfTile),
                center + new Vector2(-halfTile, halfTile)
            };
        }

        // 生成沿格子边缘的轮廓
        return GenerateGridAlignedBoundary(cells);
    }

    /// <summary>
    /// 生成沿格子边缘对齐的边界（改进版本）
    /// </summary>
    private Vector2[] GenerateGridAlignedBoundary(HashSet<Vector2I> cells)
    {
        if (cells.Count == 0)
            return new Vector2[0];

        // 找到所有外边缘的边
        var outerEdges = new List<(Vector2, Vector2)>();

        foreach (var cell in cells)
        {
            // 格子的四条边（世界坐标）
            var center = GridToWorldPosition(cell);
            float halfTile = tileSize.X * 0.5f;

            var topLeft = center + new Vector2(-halfTile, -halfTile);
            var topRight = center + new Vector2(halfTile, -halfTile);
            var bottomRight = center + new Vector2(halfTile, halfTile);
            var bottomLeft = center + new Vector2(-halfTile, halfTile);

            // 检查四个方向的邻居
            var neighbors = new Vector2I[] {
                cell + Vector2I.Up,     // 上邻居
                cell + Vector2I.Right,  // 右邻居
                cell + Vector2I.Down,   // 下邻居
                cell + Vector2I.Left    // 左邻居
            };

            var edges = new (Vector2, Vector2)[] {
                (topLeft, topRight),      // 上边
                (topRight, bottomRight),  // 右边
                (bottomRight, bottomLeft), // 下边
                (bottomLeft, topLeft)     // 左边
            };

            // 如果某个方向没有邻居，则该边是外边缘
            for (int i = 0; i < 4; i++)
            {
                if (!cells.Contains(neighbors[i]))
                {
                    outerEdges.Add(edges[i]);
                }
            }
        }

        if (outerEdges.Count == 0)
        {
            // 备用方案：单个格子
            var center = GridToWorldPosition(cells.First());
            float halfTile = tileSize.X * 0.5f;
            return new Vector2[] {
                center + new Vector2(-halfTile, -halfTile),
                center + new Vector2(halfTile, -halfTile),
                center + new Vector2(halfTile, halfTile),
                center + new Vector2(-halfTile, halfTile)
            };
        }

        // 从边缘构建轮廓
        return BuildContourFromEdges(outerEdges);
    }

    /// <summary>
    /// 从边缘列表构建连续的轮廓
    /// </summary>
    private Vector2[] BuildContourFromEdges(List<(Vector2, Vector2)> edges)
    {
        if (edges.Count == 0)
            return new Vector2[0];

        var contour = new List<Vector2>();
        var usedEdges = new HashSet<int>();

        // 从第一条边开始
        contour.Add(edges[0].Item1);
        contour.Add(edges[0].Item2);
        usedEdges.Add(0);

        // 尝试连接更多边
        while (usedEdges.Count < edges.Count)
        {
            var lastPoint = contour[contour.Count - 1];
            bool foundConnection = false;

            for (int i = 0; i < edges.Count; i++)
            {
                if (usedEdges.Contains(i))
                    continue;

                var edge = edges[i];
                const float tolerance = 0.1f;

                if (lastPoint.DistanceTo(edge.Item1) < tolerance)
                {
                    contour.Add(edge.Item2);
                    usedEdges.Add(i);
                    foundConnection = true;
                    break;
                }
                else if (lastPoint.DistanceTo(edge.Item2) < tolerance)
                {
                    contour.Add(edge.Item1);
                    usedEdges.Add(i);
                    foundConnection = true;
                    break;
                }
            }

            if (!foundConnection)
                break;
        }

        // 移除重复的点
        var uniqueContour = new List<Vector2>();
        const float duplicateTolerance = 0.1f;

        foreach (var point in contour)
        {
            bool isDuplicate = false;
            foreach (var existing in uniqueContour)
            {
                if (point.DistanceTo(existing) < duplicateTolerance)
                {
                    isDuplicate = true;
                    break;
                }
            }
            if (!isDuplicate)
            {
                uniqueContour.Add(point);
            }
        }

        return uniqueContour.ToArray();
    }

    /// <summary>
    /// 格子角点坐标转世界坐标
    /// </summary>
    private Vector2 GridCornerToWorldPosition(Vector2I gridCorner)
    {
        if (tileMapLayer != null)
        {
            return tileMapLayer.MapToLocal(gridCorner);
        }
        else
        {
            return new Vector2(gridCorner.X * tileSize.X, gridCorner.Y * tileSize.Y);
        }
    }

    /// <summary>
    /// 检查两点之间是否有视线（简化版本）
    /// </summary>
    private bool HasLineOfSight(Vector2I from, Vector2I to)
    {
        // 简化版本：直接返回true
        // 在实际项目中，这里应该检查路径上是否有障碍物
        return true;
    }

    /// <summary>
    /// 显示多边形（支持多个独立的格子）
    /// </summary>
    private void ShowPolygon(Vector2[] polygonPoints)
    {
        // 创建新的Polygon2D节点
        currentPolygon = new Polygon2D();
        currentPolygon.Polygon = polygonPoints;
        currentPolygon.Color = RangeColor;

        // 添加边框（如果启用）
        if (ShowBorder)
        {
            var line2D = new Line2D();

            // 添加所有顶点，并闭合多边形
            foreach (var point in polygonPoints)
            {
                line2D.AddPoint(point);
            }
            line2D.AddPoint(polygonPoints[0]); // 闭合多边形

            line2D.DefaultColor = BorderColor;
            line2D.Width = BorderWidth;
            line2D.Closed = true;

            currentPolygon.AddChild(line2D);
        }

        AddChild(currentPolygon);
    }

    /// <summary>
    /// 显示多个独立的格子（每个格子一个正方形）
    /// </summary>
    private void ShowCellsAsSquares(HashSet<Vector2I> cells)
    {
        // 为每个格子创建一个独立的正方形
        foreach (var cell in cells)
        {
            var cellPolygon = new Polygon2D();
            var center = GridToWorldPosition(cell);
            float halfTile = tileSize.X * 0.5f;

            Vector2[] vertices = {
                center + new Vector2(-halfTile, -halfTile),
                center + new Vector2(halfTile, -halfTile),
                center + new Vector2(halfTile, halfTile),
                center + new Vector2(-halfTile, halfTile)
            };

            cellPolygon.Polygon = vertices;
            cellPolygon.Color = RangeColor;

            // 添加边框
            if (ShowBorder)
            {
                var line2D = new Line2D();
                foreach (var vertex in vertices)
                {
                    line2D.AddPoint(vertex);
                }
                line2D.AddPoint(vertices[0]); // 闭合
                line2D.DefaultColor = BorderColor;
                line2D.Width = BorderWidth;
                line2D.Closed = true;
                cellPolygon.AddChild(line2D);
            }

            AddChild(cellPolygon);
        }
    }

    /// <summary>
    /// 清除当前显示的范围
    /// </summary>
    public void ClearRange()
    {
        // 清除所有子节点（可能有多个Polygon2D）
        foreach (Node child in GetChildren())
        {
            if (child is Polygon2D)
            {
                child.QueueFree();
            }
        }

        // 清除主多边形引用
        if (currentPolygon != null)
        {
            currentPolygon = null;
        }
    }

    /// <summary>
    /// 网格坐标转世界坐标
    /// </summary>
    private Vector2 GridToWorldPosition(Vector2I gridCoord)
    {
        if (tileMapLayer != null)
        {
            // 使用TileMapLayer的方法转换坐标
            return tileMapLayer.MapToLocal(gridCoord);
        }
        else
        {
            // 备用方法：手动计算
            return new Vector2(gridCoord.X * tileSize.X, gridCoord.Y * tileSize.Y);
        }
    }

    /// <summary>
    /// 世界坐标转网格坐标
    /// </summary>
    private Vector2I WorldToGridPosition(Vector2 worldPos)
    {
        if (tileMapLayer != null)
        {
            return tileMapLayer.LocalToMap(worldPos);
        }
        else
        {
            return new Vector2I((int)(worldPos.X / tileSize.X), (int)(worldPos.Y / tileSize.Y));
        }
    }

    /// <summary>
    /// 设置显示样式
    /// </summary>
    public void SetStyle(Color rangeColor, Color borderColor, float borderWidth = 2.0f, bool showBorder = true)
    {
        RangeColor = rangeColor;
        BorderColor = borderColor;
        BorderWidth = borderWidth;
        ShowBorder = showBorder;
    }

    /// <summary>
    /// 预设样式：攻击范围（红色）
    /// </summary>
    public void SetAttackStyle()
    {
        SetStyle(
            new Color(1, 0, 0, 0.3f),  // 半透明红色
            new Color(1, 0, 0, 0.8f),  // 红色边框
            2.0f, true
        );
    }

    /// <summary>
    /// 预设样式：移动范围（蓝色）
    /// </summary>
    public void SetMovementStyle()
    {
        SetStyle(
            new Color(0, 0, 1, 0.3f),  // 半透明蓝色
            new Color(0, 0, 1, 0.8f),  // 蓝色边框
            2.0f, true
        );
    }

    /// <summary>
    /// 预设样式：治疗范围（绿色）
    /// </summary>
    public void SetHealStyle()
    {
        SetStyle(
            new Color(0, 1, 0, 0.3f),  // 半透明绿色
            new Color(0, 1, 0, 0.8f),  // 绿色边框
            2.0f, true
        );
    }

    /// <summary>
    /// 预设样式：视野范围（黄色）
    /// </summary>
    public void SetViewStyle()
    {
        SetStyle(
            new Color(1, 1, 0, 0.2f),  // 半透明黄色
            new Color(1, 1, 0, 0.6f),  // 黄色边框
            1.5f, true
        );
    }

    /// <summary>
    /// 预设样式：警戒范围（橙色）
    /// </summary>
    public void SetAlertStyle()
    {
        SetStyle(
            new Color(1, 0.5f, 0, 0.25f),  // 半透明橙色
            new Color(1, 0.5f, 0, 0.7f),   // 橙色边框
            2.0f, true
        );
    }

    /// <summary>
    /// 预设样式：距离范围（紫色）
    /// </summary>
    public void SetDistanceStyle()
    {
        SetStyle(
            new Color(0.8f, 0.4f, 1, 0.25f),  // 半透明紫色
            new Color(0.8f, 0.4f, 1, 0.7f),   // 紫色边框
            2.0f, true
        );
    }

    /// <summary>
    /// 预设样式：扇形范围（青色）
    /// </summary>
    public void SetSectorStyle()
    {
        SetStyle(
            new Color(0, 1, 1, 0.3f),  // 半透明青色
            new Color(0, 1, 1, 0.8f),  // 青色边框
            2.5f, true
        );
    }

    /// <summary>
    /// 获取当前是否正在显示范围
    /// </summary>
    public bool IsShowingRange()
    {
        return currentPolygon != null && IsInstanceValid(currentPolygon);
    }

    /// <summary>
    /// 析构时清理资源
    /// </summary>
    public override void _ExitTree()
    {
        ClearRange();
        base._ExitTree();
    }
}
