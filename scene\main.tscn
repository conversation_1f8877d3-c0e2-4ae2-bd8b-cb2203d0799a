[gd_scene load_steps=29 format=3 uid="uid://c6ie7hx3yxkyi"]

[ext_resource type="PackedScene" uid="uid://cl27cvwhqh26w" path="res://tool/Grid/grid_blue.tscn" id="1_itxqv"]
[ext_resource type="Script" uid="uid://dy4u2s7e4lpb7" path="res://scene/main.gd" id="1_p1hux"]
[ext_resource type="Texture2D" uid="uid://fhw4hesvlb88" path="res://assetes/mine/tile_basic/char_basic.png" id="2_wp4xf"]
[ext_resource type="Script" uid="uid://33xook1k137i" path="res://import_script/input.gd" id="2_wscrc"]
[ext_resource type="Script" uid="uid://bhxieobaecmsv" path="res://tool/tilemap/char_show.gd" id="4_of57s"]
[ext_resource type="Shader" uid="uid://ja1s2eixrwj5" path="res://tool/shader/brackground_gd.gdshader" id="5_tqoya"]
[ext_resource type="Script" uid="uid://dv8en84d3m4m7" path="res://scene/trun_management.gd" id="7_0vlqb"]
[ext_resource type="Script" uid="uid://c3p5qcfh5jy3d" path="res://c_sharp/import_gd/csharp_map.cs" id="7_gjnwh"]
[ext_resource type="Texture2D" uid="uid://cpwo1664o4hqo" path="res://assetes/mine/tilemap/grass.png" id="7_jcbur"]
[ext_resource type="Texture2D" uid="uid://laxwfldtoit5" path="res://assetes/mine/tilemap/sand.png" id="7_v2gmf"]
[ext_resource type="FastNoiseLite" uid="uid://dhyrcf6exeaaf" path="res://scene/main.tres" id="8_gjnwh"]
[ext_resource type="Script" uid="uid://dondd0fr1q3hg" path="res://c_sharp/import_gd/object_man.cs" id="10_fdqew"]
[ext_resource type="PackedScene" uid="uid://ygdgiasuailc" path="res://tool/focus/focus.tscn" id="10_mswub"]
[ext_resource type="Script" uid="uid://ch2wuqela4uph" path="res://tool/Grid/show_grid.gd" id="11_hybxs"]
[ext_resource type="PackedScene" uid="uid://dscq21bg28eqy" path="res://tool/Grid/grid_rad.tscn" id="12_hybxs"]
[ext_resource type="Script" uid="uid://6ri4680khr2g" path="res://tool/state/fms.gd" id="16_tv8i1"]
[ext_resource type="Script" uid="uid://4d5rvf74jwhr" path="res://tool/state/game_begin.gd" id="17_smf0o"]
[ext_resource type="Script" uid="uid://hub4lunae77q" path="res://tool/state/player_turn.gd" id="18_tv8i1"]
[ext_resource type="Script" uid="uid://c51u2qs5dgt5a" path="res://tool/state/enemy_turn.gd" id="19_0vlqb"]
[ext_resource type="PackedScene" uid="uid://c1etvk0lykq43" path="res://tool/main_cam/camera_2d.tscn" id="19_gjnwh"]
[ext_resource type="Script" uid="uid://dwb7b0if44x42" path="res://c_sharp/skill_range/SkillRangeExample.cs" id="21_fdqew"]
[ext_resource type="PackedScene" uid="uid://d4ggmxl8dd4t2" path="res://tool/ui/ui.tscn" id="22_0d4pv"]
[ext_resource type="Script" uid="uid://cn553wry5lqto" path="res://c_sharp/ui/MapDisplayUI.cs" id="23_gtqqq"]

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_0ha60"]
texture = ExtResource("2_wp4xf")
texture_region_size = Vector2i(32, 32)
0:0/next_alternative_id = 4
0:0/0 = 0
1:0/0 = 0
2:0/0 = 0
3:0/0 = 0
4:0/0 = 0
5:0/0 = 0
5:1/0 = 0

[sub_resource type="TileSet" id="TileSet_4j5ol"]
tile_size = Vector2i(32, 32)
sources/0 = SubResource("TileSetAtlasSource_0ha60")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_brvut"]
shader = ExtResource("5_tqoya")
shader_parameter/image_r = ExtResource("7_jcbur")
shader_parameter/color_g = Color(0, 0, 0, 1)
shader_parameter/image_b = ExtResource("7_v2gmf")

[sub_resource type="TileSetScenesCollectionSource" id="TileSetScenesCollectionSource_hybxs"]
scenes/1/scene = ExtResource("1_itxqv")
scenes/2/scene = ExtResource("12_hybxs")

[sub_resource type="TileSet" id="TileSet_v2gmf"]
tile_size = Vector2i(32, 32)
sources/0 = SubResource("TileSetScenesCollectionSource_hybxs")

[node name="main" type="Node2D"]
script = ExtResource("1_p1hux")

[node name="fms" type="Node" parent="."]
script = ExtResource("16_tv8i1")

[node name="game_begin" type="Node" parent="fms"]
script = ExtResource("17_smf0o")

[node name="battle_mode" type="Node" parent="fms"]
script = ExtResource("18_tv8i1")

[node name="free_mode" type="Node" parent="fms"]
script = ExtResource("19_0vlqb")

[node name="input" type="Node2D" parent="."]
script = ExtResource("2_wscrc")

[node name="map_management" type="Node2D" parent="."]
script = ExtResource("7_gjnwh")
Noise = ExtResource("8_gjnwh")

[node name="char_show" type="TileMapLayer" parent="map_management"]
y_sort_enabled = true
tile_set = SubResource("TileSet_4j5ol")
script = ExtResource("4_of57s")

[node name="bg_node" type="Node2D" parent="map_management"]
position = Vector2(16, 16)

[node name="Node2D" type="Node2D" parent="." node_paths=PackedStringArray("tileMapLayer")]
script = ExtResource("21_fdqew")
tileMapLayer = NodePath("../map_management/char_show")

[node name="object_management" type="Node2D" parent="."]
script = ExtResource("10_fdqew")
Noise = ExtResource("8_gjnwh")

[node name="turn_management" type="Node2D" parent="."]
script = ExtResource("7_0vlqb")

[node name="show_grid" type="TileMapLayer" parent="turn_management"]
y_sort_enabled = true
material = SubResource("ShaderMaterial_brvut")
tile_set = SubResource("TileSet_v2gmf")
script = ExtResource("11_hybxs")

[node name="Focus" parent="turn_management" instance=ExtResource("10_mswub")]

[node name="Camera2D" parent="." instance=ExtResource("19_gjnwh")]

[node name="ui" parent="Camera2D" instance=ExtResource("22_0d4pv")]

[node name="map" type="Control" parent="Camera2D"]
layout_mode = 3
anchors_preset = 0
script = ExtResource("23_gtqqq")
