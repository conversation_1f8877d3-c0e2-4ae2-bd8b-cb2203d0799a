using Godot;
using System;
using GameEnum;
using delaunay_algorithm;
using System.Collections.Generic;
using Godot.NativeInterop;
using System.Linq;


public partial class Map_res : Resource
{
    // 使用 HashSet<Vector2I>（需实现相等比较器）
    public class Vector2IEqualityComparer : IEqualityComparer<Vector2I>
    {
        public bool Equals(Vector2I a, Vector2I b) => a == b;
        public int GetHashCode(Vector2I v) => v.X.GetHashCode() ^ v.Y.GetHashCode();
    }
    //地图噪声
    [Export] public FastNoiseLite Noise { get; set; }
    private int edge_left = new();
    private int edge_right = new();
    private int edge_top = new();
    private int edge_bottom = new();

    // 公共属性用于外部访问
    public int EdgeLeft => edge_left;
    public int EdgeRight => edge_right;
    public int EdgeTop => edge_top;
    public int EdgeBottom => edge_bottom;
    private Vector2I block_size = new(); // 区块大小
    private int block_show_quantity = new(); // 区块显示数量
    private RandomNumberGenerator rng = new RandomNumberGenerator();
    public List<region_mes> reg_list = new List<region_mes>(); // 区域列表
    //主干道
    // 新的优化道路系统
    public OptimizedRoadSystem roadSystem = new OptimizedRoadSystem();// 全局道路查询
    public SimpleRoomPreloadSystem simpleRoomPreloadSystem;// = new SimpleRoomPreloadSystem();
    public void map_res_init(FastNoiseLite noise, int edge_left, int edge_right, int edge_top, int edge_bottom, Vector2I block_size, int block_show_quantity)
    {
        rng.Seed = 0;

        this.Noise = noise;
        if (this.Noise == null)
        {
            GD.PrintErr("地图噪声未设置，请先设置地图噪声");
            return;
        }
        this.edge_left = edge_left;
        this.edge_right = edge_right;
        this.edge_top = edge_top;
        this.edge_bottom = edge_bottom;
        this.block_size = block_size;
        this.block_show_quantity = block_show_quantity;

        // GD.Print($"宽 {Math.Abs(edge_right - edge_left)} 高{Math.Abs(edge_top - edge_bottom)}");
        simpleRoomPreloadSystem = new SimpleRoomPreloadSystem(block_size.X,500);

        //中心点随机生成
        List<Vector2I> points = region_central_creat(20, 1000);
        // 打印生成的点数
        GD.Print($"Generated {points.Count} points");

        // 按 X 坐标升序排序
        points.Sort((a, b) => a.X.CompareTo(b.X));

        //生成道路
        Delaunay(points);
        
        // 创建区域列表
        PopulateRegionList();

        foreach (region_mes city in reg_list)
        {
            creat_room(city);
        }
    }

    //地图生成
    //生成随机的中心点
    public List<Vector2I> region_central_creat(int cnt, int minDistance = 500, int borderMargin = 96)
    {
        int width = Math.Abs(edge_right - edge_left) - 2 * borderMargin;
        int height = Math.Abs(edge_top - edge_bottom) - 2 * borderMargin;
        var IntegerPoissonDiskSampling = new IntegerPoissonDiskSampling(rng.Seed, width, height, minDistance);
        List<Vector2I> points = IntegerPoissonDiskSampling.GeneratePoints(cnt);
        for (int i = 0; i < points.Count; i++)
        {
            points[i] = points[i] + new Vector2I(edge_left + borderMargin, edge_top + borderMargin);
        }
        return points;
    }
    // 存储处理后的Delaunay结果
    public DelaunayProcessor.ProcessedResult processedResult;

    public void Delaunay(List<Vector2I> points)
    {
        List<DelaunayTriangle> allTriangle = new List<DelaunayTriangle>();//delaunay三角形集合
        //全部点位
        List<Site> sitesP = new List<Site>();
        //顶点连接
        Voronoi voroObject = new Voronoi();
        ulong time_cnt = Time.GetTicksMsec();

        foreach (Vector2I p in points)
        {
            Site site = new Site(p.X, p.Y);
            sitesP.Add(site);
        }

        GD.Print($"输入的中心点数量: {points.Count}");
        // 调试：输出地图边界参数
        GD.Print($"地图边界参数: left={edge_left}, right={edge_right}, top={edge_top}, bottom={edge_bottom}");

        //计算超级三角形大小 - 基于实际地图边界
        double width = Math.Abs(edge_right - edge_left);
        double height = Math.Abs(edge_top - edge_bottom); // 修复：edge_top - edge_bottom
        double buffer_x = Math.Max(width * 0.5, 1000); // 至少1000单位的缓冲
        double buffer_y = Math.Max(height * 0.5, 1000);

        // 确定实际的边界（考虑edge_top可能小于edge_bottom的情况）
        double actual_left = Math.Min(edge_left, edge_right);
        double actual_right = Math.Max(edge_left, edge_right);
        double actual_top = Math.Min(edge_top, edge_bottom);
        double actual_bottom = Math.Max(edge_top, edge_bottom);

        //将超级三角形的三点添加到三角形网中 - 创建一个足够大的超级三角形
        Site A = new Site(actual_left - buffer_x, actual_bottom + buffer_y);   // 左下角外侧
        Site B = new Site(actual_right + buffer_x, actual_bottom + buffer_y);  // 右下角外侧
        Site C = new Site((actual_left + actual_right) / 2, actual_top - buffer_y); // 顶部中央外侧

        GD.Print($"生成的超级三角形顶点:");
        GD.Print($"  A: ({A.x:F1}, {A.y:F1}) - 左下角外侧");
        GD.Print($"  B: ({B.x:F1}, {B.y:F1}) - 右下角外侧");
        GD.Print($"  C: ({C.x:F1}, {C.y:F1}) - 顶部中央外侧");

        DelaunayTriangle dt = new DelaunayTriangle(A, B, C);
        allTriangle.Add(dt);

        //构造Delaunay三角形网
        voroObject.setDelaunayTriangle(allTriangle, sitesP);

        GD.Print($"Total triangles before processing: {allTriangle.Count}");
        GD.Print($"Super triangle: A({A.x}, {A.y}), B({B.x}, {B.y}), C({C.x}, {C.y})");

        // 使用新的处理器去除超级三角形并获得正确结果
        processedResult = DelaunayProcessor.ProcessDelaunayResult(allTriangle, sitesP, A, B, C);

        //获取处理后的边（去除了超级三角形的影响）
        GD.Print("有效Delaunay边数量：" + processedResult.ValidEdges.Count);

        //获取处理后的Voronoi边
        GD.Print("有效Voronoi边数量：" + processedResult.VoronoiEdges.Count);

        // 打印Voronoi单元格信息
        GD.Print($"生成了 {processedResult.VoronoiCells.Count} 个Voronoi单元格");
        GD.Print($"输入点数 vs Voronoi单元格数: {points.Count} vs {processedResult.VoronoiCells.Count}");
        if (points.Count != processedResult.VoronoiCells.Count)
        {
            GD.PrintErr($"⚠️ 数量不匹配！输入了 {points.Count} 个点，但得到了 {processedResult.VoronoiCells.Count} 个Voronoi单元格");

            // 分析可能的原因
            GD.Print("可能的原因分析:");
            GD.Print("1. 某些输入点重复或过于接近");
            GD.Print("2. 某些点在超级三角形过滤时被误删");
            GD.Print("3. Voronoi算法实现有问题");
            GD.Print("4. 某些点位于地图边界外");
        }
        foreach (var cell in processedResult.VoronoiCells)
        {
            GD.Print($"单元格中心: ({cell.CenterSite.x:F1}, {cell.CenterSite.y:F1}), 面积: {cell.Area:F2}, 邻居数: {cell.Neighbors.Count}");
        }

        // 生成优化的道路系统
        GenerateOptimizedRoadSystem();

        // 测试道路分离存储功能
        RoadSystemTest.TestRoadSeparation();

        // 测试缓存性能
        RoadSystemTest.TestCachePerformance();

        GD.Print("加载耗时间:", Time.GetTicksMsec() - time_cnt);
    }


    /// <summary>
    /// 生成优化的道路系统（新方法）
    /// </summary>
    private void GenerateOptimizedRoadSystem()
    {
        if (processedResult == null || processedResult.ValidEdges.Count == 0)
        {
            GD.Print("没有有效边数据，跳过道路生成");
            return;
        }

        // 配置道路系统参数
        roadSystem.MergeThreshold = block_size.X * 2;
        roadSystem.DefaultRoadWidth = 8;

        GD.Print($"道路系统配置: MergeThreshold={roadSystem.MergeThreshold}, RoadWidth={roadSystem.DefaultRoadWidth}");

        // 生成道路系统
        roadSystem.GenerateOptimizedRoadSystem(processedResult.ValidEdges);

        GD.Print($"优化道路系统生成完成");
        GD.Print(roadSystem.GetStats());
    }


    /// <summary>
    /// 检查坐标是否是全局道路（主路）
    /// </summary>
    public bool IsGlobalRoad(Vector2I coordinate)
    {
        return roadSystem.IsOnMainRoad(coordinate);
    }

    /// <summary>
    /// 检查坐标是否是小路
    /// </summary>
    public bool IsLocalRoad(Vector2I coordinate)
    {
        return roadSystem.IsOnLocalRoad(coordinate);
    }

    /// <summary>
    /// 检查坐标是否在任何道路上（主路或小路）
    /// </summary>
    public bool IsAnyRoad(Vector2I coordinate)
    {
        return roadSystem.IsOnRoad(coordinate);
    }

    public void updata_area_roompreload(Rect2I rect)
    {
        simpleRoomPreloadSystem.UpdateQueryArea(rect);
    }

    //判断点位是否在房间中
    public bool is_room(Vector2I pos)
    {
        return simpleRoomPreloadSystem.GetRoomAtPosition(pos) != null;
    }

    /// <summary>
    /// 根据Voronoi结果填充reg_list
    /// </summary>
    private void PopulateRegionList()
    {
        reg_list.Clear();

        if (processedResult == null || processedResult.VoronoiCells.Count == 0)
        {
            GD.Print("没有Voronoi单元格数据");
            return;
        }

        GD.Print($"开始处理 {processedResult.VoronoiCells.Count} 个Voronoi单元格");

        // 为每个Voronoi单元格创建region_mes
        foreach (var cell in processedResult.VoronoiCells)
        {
            // 手动创建region_mes而不是使用FromVoronoiCell
            var region = new region_mes();
            region.central_coord = new Vector2I((int)cell.CenterSite.x, (int)cell.CenterSite.y);
            region.BoundaryVertices = new List<Vector2>(cell.Vertices);

            // 计算建筑区域
            region.CalculateBuildingArea();

            // 根据道路分割建筑区域
            region.SplitBuildingAreasByRoads(roadSystem);

            // 优化：只使用处理后的有效边来计算连接信息，确保数据一致性
            // 这样可以确保邻居数量基于实际存在的边，而不是原始的Voronoi邻居
            foreach (var edge in processedResult.ValidEdges)
            {
                var pointA = new Vector2I((int)edge.a.x, (int)edge.a.y);
                var pointB = new Vector2I((int)edge.b.x, (int)edge.b.y);

                if (pointA == region.central_coord)
                {
                    if (!region.ConnectedPoints.Contains(pointB))
                    {
                        region.ConnectedPoints.Add(pointB);
                    }
                    region.ConnectionEdges[pointB] = edge;
                }
                else if (pointB == region.central_coord)
                {
                    if (!region.ConnectedPoints.Contains(pointA))
                    {
                        region.ConnectedPoints.Add(pointA);
                    }
                    region.ConnectionEdges[pointA] = edge;
                }
            }

            // 设置区域类型和随机名称
            string regionType = "村庄";
            if (cell.Area > 50000)
            {
                regionType = region.ConnectedPoints.Count >= 6 ? "大型城市" : "大型城镇";
            }
            else if (cell.Area > 20000)
            {
                regionType = region.ConnectedPoints.Count >= 4 ? "中型城市" : "中型城镇";
            }

            // 生成随机区域名称
            string randomName = RandomNameGenerator.GeneratePlaceName();
            region.region_name = $"{randomName}({regionType})";

            // 添加到列表
            reg_list.Add(region);

            // 调试信息：对比原始邻居数量和实际连接数量
            int originalNeighborCount = cell.Neighbors.Count;
            int actualConnectionCount = region.ConnectedPoints.Count;

            GD.Print($"区域[{region.central_coord.X},{region.central_coord.Y}]: {regionType}, 面积{cell.Area:F0}");
            GD.Print($"  原始邻居数: {originalNeighborCount}, 实际连接数: {actualConnectionCount}, 差异: {originalNeighborCount - actualConnectionCount}");
            GD.Print($"  建筑区域数量: {region.BuildingAreas.Count}, 总建筑面积: {region.GetBuildingAreaSize()}");

        }

        GD.Print($"成功创建 {reg_list.Count} 个区域");
    }

    public void creat_room(region_mes city)
    {
        RoomAndSmallRoadGenerator roomAndSmallRoadGenerator =new RoomAndSmallRoadGenerator(simpleRoomPreloadSystem, roadSystem);
        GD.Print($"开始为区域 {city.central_coord} 生成建筑和小路");

        // 为每个建筑区域生成建筑和小路
        foreach (var buildingArea in city.BuildingAreas)
        {
            roomAndSmallRoadGenerator.GenerateBuildingsAndLocalRoads(buildingArea, city);
        }
    }

}
