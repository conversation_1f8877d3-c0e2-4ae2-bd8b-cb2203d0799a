using Godot;
using System;
using System.Collections.Generic;

/// <summary>
/// 道路系统分离存储测试类
/// </summary>
public partial class RoadSystemTest : RefCounted
{
    /// <summary>
    /// 测试道路分离存储功能
    /// </summary>
    public static void TestRoadSeparation()
    {
        GD.Print("🧪 开始测试道路分离存储功能");
        
        var roadSystem = new OptimizedRoadSystem();
        
        // 测试1: 添加主路
        GD.Print("📍 测试1: 添加主路");
        var mainRoad1 = new OptimizedRoadSystem.RoadSegment
        {
            StartPoint = new Vector2I(0, 0),
            EndPoint = new Vector2I(100, 0),
            Width = 8,
            Type = OptimizedRoadSystem.RoadType.MainRoad
        };
        
        var mainRoad2 = new OptimizedRoadSystem.RoadSegment
        {
            StartPoint = new Vector2I(50, -50),
            EndPoint = new Vector2I(50, 50),
            Width = 8,
            Type = OptimizedRoadSystem.RoadType.MainRoad
        };
        
        roadSystem.AddRoadSegment(mainRoad1);
        roadSystem.AddRoadSegment(mainRoad2);
        
        // 测试2: 添加小路
        GD.Print("📍 测试2: 添加小路");
        var localRoad1 = new OptimizedRoadSystem.RoadSegment
        {
            StartPoint = new Vector2I(10, 10),
            EndPoint = new Vector2I(40, 10),
            Width = 4,
            Type = OptimizedRoadSystem.RoadType.LocalRoad
        };
        
        var localRoad2 = new OptimizedRoadSystem.RoadSegment
        {
            StartPoint = new Vector2I(60, 10),
            EndPoint = new Vector2I(90, 10),
            Width = 4,
            Type = OptimizedRoadSystem.RoadType.LocalRoad
        };
        
        roadSystem.AddRoadSegment(localRoad1);
        roadSystem.AddRoadSegment(localRoad2);
        
        // 测试3: 验证分离存储
        GD.Print("📍 测试3: 验证分离存储");
        var allRoads = roadSystem.GetAllRoadSegments();
        var mainRoads = roadSystem.GetMainRoadSegments();
        var localRoads = roadSystem.GetLocalRoadSegments();
        
        GD.Print($"   总道路数: {allRoads.Count} (期望: 4)");
        GD.Print($"   主路数: {mainRoads.Count} (期望: 2)");
        GD.Print($"   小路数: {localRoads.Count} (期望: 2)");
        
        // 验证结果
        bool testPassed = allRoads.Count == 4 && mainRoads.Count == 2 && localRoads.Count == 2;
        
        // 测试4: 验证类型过滤
        GD.Print("📍 测试4: 验证类型过滤");
        var mainRoadsByType = roadSystem.GetRoadSegmentsByType(OptimizedRoadSystem.RoadType.MainRoad);
        var localRoadsByType = roadSystem.GetRoadSegmentsByType(OptimizedRoadSystem.RoadType.LocalRoad);
        
        GD.Print($"   按类型获取主路数: {mainRoadsByType.Count} (期望: 2)");
        GD.Print($"   按类型获取小路数: {localRoadsByType.Count} (期望: 2)");
        
        testPassed = testPassed && mainRoadsByType.Count == 2 && localRoadsByType.Count == 2;
        
        // 测试5: 清除特定类型道路
        GD.Print("📍 测试5: 清除小路");
        roadSystem.ClearRoadsByType(OptimizedRoadSystem.RoadType.LocalRoad);
        
        var allRoadsAfterClear = roadSystem.GetAllRoadSegments();
        var mainRoadsAfterClear = roadSystem.GetMainRoadSegments();
        var localRoadsAfterClear = roadSystem.GetLocalRoadSegments();
        
        GD.Print($"   清除小路后总道路数: {allRoadsAfterClear.Count} (期望: 2)");
        GD.Print($"   清除小路后主路数: {mainRoadsAfterClear.Count} (期望: 2)");
        GD.Print($"   清除小路后小路数: {localRoadsAfterClear.Count} (期望: 0)");
        
        testPassed = testPassed && allRoadsAfterClear.Count == 2 && 
                    mainRoadsAfterClear.Count == 2 && localRoadsAfterClear.Count == 0;
        
        // 测试6: 统计信息
        GD.Print("📍 测试6: 统计信息");
        string stats = roadSystem.GetStats();
        GD.Print($"   统计信息: {stats}");
        
        // 最终结果
        if (testPassed)
        {
            GD.Print("✅ 道路分离存储测试通过！");
        }
        else
        {
            GD.PrintErr("❌ 道路分离存储测试失败！");
        }
    }
    
    /// <summary>
    /// 测试建筑生成器的主路查找功能
    /// </summary>
    public static void TestMainRoadFinding()
    {
        GD.Print("🧪 开始测试建筑生成器的主路查找功能");
        
        var roadSystem = new OptimizedRoadSystem();
        var simpleRoomPreloadSystem = new SimpleRoomPreloadSystem(64, 500);
        var generator = new RoomAndSmallRoadGenerator(simpleRoomPreloadSystem, roadSystem);
        
        // 添加一些主路
        var mainRoad = new OptimizedRoadSystem.RoadSegment
        {
            StartPoint = new Vector2I(0, 50),
            EndPoint = new Vector2I(200, 50),
            Width = 8,
            Type = OptimizedRoadSystem.RoadType.MainRoad
        };
        roadSystem.AddRoadSegment(mainRoad);
        
        // 创建一个测试区域
        var testArea = new Rect2I(100, 100, 80, 60);
        var testRegion = new region_mes();
        
        // 测试建筑和小路生成
        GD.Print("📍 测试建筑和小路生成");
        generator.GenerateBuildingsAndLocalRoads(testArea, testRegion);
        
        // 验证小路是否被正确添加
        var localRoads = roadSystem.GetLocalRoadSegments();
        GD.Print($"   生成的小路数量: {localRoads.Count}");
        
        if (localRoads.Count > 0)
        {
            GD.Print("✅ 建筑生成器主路查找测试通过！");
        }
        else
        {
            GD.Print("⚠️ 建筑生成器可能没有生成小路，请检查区域大小和配置");
        }
    }
}
