using Godot;
using System;
using System.Collections.Generic;

/// <summary>
/// 道路系统分离存储测试类
/// </summary>
public partial class RoadSystemTest : RefCounted
{
    /// <summary>
    /// 测试道路分离存储功能
    /// </summary>
    public static void TestRoadSeparation()
    {
        GD.Print("🧪 开始测试道路分离存储功能");
        
        var roadSystem = new OptimizedRoadSystem();
        
        // 测试1: 添加主路
        GD.Print("📍 测试1: 添加主路");
        var mainRoad1 = new OptimizedRoadSystem.RoadSegment
        {
            StartPoint = new Vector2I(0, 0),
            EndPoint = new Vector2I(100, 0),
            Width = 8,
            Type = OptimizedRoadSystem.RoadType.MainRoad
        };
        
        var mainRoad2 = new OptimizedRoadSystem.RoadSegment
        {
            StartPoint = new Vector2I(50, -50),
            EndPoint = new Vector2I(50, 50),
            Width = 8,
            Type = OptimizedRoadSystem.RoadType.MainRoad
        };
        
        roadSystem.AddRoadSegment(mainRoad1);
        roadSystem.AddRoadSegment(mainRoad2);
        
        // 测试2: 添加小路
        GD.Print("📍 测试2: 添加小路");
        var localRoad1 = new OptimizedRoadSystem.RoadSegment
        {
            StartPoint = new Vector2I(10, 10),
            EndPoint = new Vector2I(40, 10),
            Width = 4,
            Type = OptimizedRoadSystem.RoadType.LocalRoad
        };
        
        var localRoad2 = new OptimizedRoadSystem.RoadSegment
        {
            StartPoint = new Vector2I(60, 10),
            EndPoint = new Vector2I(90, 10),
            Width = 4,
            Type = OptimizedRoadSystem.RoadType.LocalRoad
        };
        
        roadSystem.AddRoadSegment(localRoad1);
        roadSystem.AddRoadSegment(localRoad2);
        
        // 测试3: 验证分离存储
        GD.Print("📍 测试3: 验证分离存储");
        var allRoads = roadSystem.GetAllRoadSegments();
        var mainRoads = roadSystem.GetMainRoadSegments();
        var localRoads = roadSystem.GetLocalRoadSegments();
        
        GD.Print($"   总道路数: {allRoads.Count} (期望: 4)");
        GD.Print($"   主路数: {mainRoads.Count} (期望: 2)");
        GD.Print($"   小路数: {localRoads.Count} (期望: 2)");
        
        // 验证结果
        bool testPassed = allRoads.Count == 4 && mainRoads.Count == 2 && localRoads.Count == 2;
        
        // 测试4: 验证类型过滤
        GD.Print("📍 测试4: 验证类型过滤");
        var mainRoadsByType = roadSystem.GetRoadSegmentsByType(OptimizedRoadSystem.RoadType.MainRoad);
        var localRoadsByType = roadSystem.GetRoadSegmentsByType(OptimizedRoadSystem.RoadType.LocalRoad);
        
        GD.Print($"   按类型获取主路数: {mainRoadsByType.Count} (期望: 2)");
        GD.Print($"   按类型获取小路数: {localRoadsByType.Count} (期望: 2)");
        
        testPassed = testPassed && mainRoadsByType.Count == 2 && localRoadsByType.Count == 2;
        
        // 测试5: 清除特定类型道路
        GD.Print("📍 测试5: 清除小路");
        roadSystem.ClearRoadsByType(OptimizedRoadSystem.RoadType.LocalRoad);
        
        var allRoadsAfterClear = roadSystem.GetAllRoadSegments();
        var mainRoadsAfterClear = roadSystem.GetMainRoadSegments();
        var localRoadsAfterClear = roadSystem.GetLocalRoadSegments();
        
        GD.Print($"   清除小路后总道路数: {allRoadsAfterClear.Count} (期望: 2)");
        GD.Print($"   清除小路后主路数: {mainRoadsAfterClear.Count} (期望: 2)");
        GD.Print($"   清除小路后小路数: {localRoadsAfterClear.Count} (期望: 0)");
        
        testPassed = testPassed && allRoadsAfterClear.Count == 2 && 
                    mainRoadsAfterClear.Count == 2 && localRoadsAfterClear.Count == 0;
        
        // 测试6: 测试分离的道路判断方法
        GD.Print("📍 测试6: 测试分离的道路判断方法");

        // 重新添加一些道路用于测试
        roadSystem.AddRoadSegment(mainRoad1);
        roadSystem.AddRoadSegment(localRoad1);

        // 测试主路判断
        bool isOnMainRoad = roadSystem.IsOnMainRoad(new Vector2I(50, 0)); // 应该在主路上
        bool isNotOnMainRoad = roadSystem.IsOnMainRoad(new Vector2I(25, 10)); // 应该不在主路上

        // 测试小路判断
        bool isOnLocalRoad = roadSystem.IsOnLocalRoad(new Vector2I(25, 10)); // 应该在小路上
        bool isNotOnLocalRoad = roadSystem.IsOnLocalRoad(new Vector2I(50, 0)); // 应该不在小路上（在主路上）

        // 测试道路类型获取
        var roadTypeMain = roadSystem.GetRoadTypeAtCoordinate(new Vector2I(50, 0));
        var roadTypeLocal = roadSystem.GetRoadTypeAtCoordinate(new Vector2I(25, 10));
        var roadTypeNone = roadSystem.GetRoadTypeAtCoordinate(new Vector2I(200, 200));

        GD.Print($"   主路判断测试: 在主路={isOnMainRoad}, 不在主路={!isNotOnMainRoad}");
        GD.Print($"   小路判断测试: 在小路={isOnLocalRoad}, 不在小路={!isNotOnLocalRoad}");
        GD.Print($"   道路类型测试: 主路={roadTypeMain}, 小路={roadTypeLocal}, 无道路={roadTypeNone}");

        bool roadTypeTestPassed = isOnMainRoad && !isNotOnMainRoad && isOnLocalRoad && !isNotOnLocalRoad &&
                                 roadTypeMain == OptimizedRoadSystem.RoadType.MainRoad &&
                                 roadTypeLocal == OptimizedRoadSystem.RoadType.LocalRoad &&
                                 roadTypeNone == null;

        testPassed = testPassed && roadTypeTestPassed;

        // 测试7: 统计信息
        GD.Print("📍 测试7: 统计信息");
        string stats = roadSystem.GetStats();
        GD.Print($"   统计信息: {stats}");

        // 最终结果
        if (testPassed)
        {
            GD.Print("✅ 道路分离存储测试通过！");
        }
        else
        {
            GD.PrintErr("❌ 道路分离存储测试失败！");
        }
    }
    
    /// <summary>
    /// 测试建筑生成器的主路查找功能
    /// </summary>
    public static void TestMainRoadFinding()
    {
        GD.Print("🧪 开始测试建筑生成器的主路查找功能");
        
        var roadSystem = new OptimizedRoadSystem();
        var simpleRoomPreloadSystem = new SimpleRoomPreloadSystem(64, 500);
        var generator = new RoomAndSmallRoadGenerator(simpleRoomPreloadSystem, roadSystem);
        
        // 添加一些主路
        var mainRoad = new OptimizedRoadSystem.RoadSegment
        {
            StartPoint = new Vector2I(0, 50),
            EndPoint = new Vector2I(200, 50),
            Width = 8,
            Type = OptimizedRoadSystem.RoadType.MainRoad
        };
        roadSystem.AddRoadSegment(mainRoad);
        
        // 创建一个测试区域
        var testArea = new Rect2I(100, 100, 80, 60);
        var testRegion = new region_mes();
        
        // 测试建筑和小路生成
        GD.Print("📍 测试建筑和小路生成");
        generator.GenerateBuildingsAndLocalRoads(testArea, testRegion);
        
        // 验证小路是否被正确添加
        var localRoads = roadSystem.GetLocalRoadSegments();
        GD.Print($"   生成的小路数量: {localRoads.Count}");
        
        if (localRoads.Count > 0)
        {
            GD.Print("✅ 建筑生成器主路查找测试通过！");
        }
        else
        {
            GD.Print("⚠️ 建筑生成器可能没有生成小路，请检查区域大小和配置");
        }
    }

    /// <summary>
    /// 测试缓存性能
    /// </summary>
    public static void TestCachePerformance()
    {
        GD.Print("🧪 开始测试缓存性能");

        var roadSystem = new OptimizedRoadSystem();

        // 添加一些道路
        var mainRoad = new OptimizedRoadSystem.RoadSegment
        {
            StartPoint = new Vector2I(0, 0),
            EndPoint = new Vector2I(1000, 0),
            Width = 8,
            Type = OptimizedRoadSystem.RoadType.MainRoad
        };

        var localRoad = new OptimizedRoadSystem.RoadSegment
        {
            StartPoint = new Vector2I(100, 100),
            EndPoint = new Vector2I(900, 100),
            Width = 4,
            Type = OptimizedRoadSystem.RoadType.LocalRoad
        };

        roadSystem.AddRoadSegment(mainRoad);
        roadSystem.AddRoadSegment(localRoad);

        // 测试大量查询的性能
        var testPoints = new List<Vector2I>();
        for (int i = 0; i < 1000; i++)
        {
            testPoints.Add(new Vector2I(i, 0)); // 主路上的点
            testPoints.Add(new Vector2I(i + 100, 100)); // 小路上的点
            testPoints.Add(new Vector2I(i, 200)); // 不在道路上的点
        }

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // 第一次查询（无缓存）
        int mainRoadHits = 0;
        int localRoadHits = 0;

        foreach (var point in testPoints)
        {
            if (roadSystem.IsOnMainRoad(point)) mainRoadHits++;
            if (roadSystem.IsOnLocalRoad(point)) localRoadHits++;
        }

        var firstRunTime = stopwatch.ElapsedMilliseconds;
        stopwatch.Restart();

        // 第二次查询（有缓存）
        int mainRoadHits2 = 0;
        int localRoadHits2 = 0;

        foreach (var point in testPoints)
        {
            if (roadSystem.IsOnMainRoad(point)) mainRoadHits2++;
            if (roadSystem.IsOnLocalRoad(point)) localRoadHits2++;
        }

        var secondRunTime = stopwatch.ElapsedMilliseconds;
        stopwatch.Stop();

        GD.Print($"   第一次查询: {firstRunTime}ms (主路命中: {mainRoadHits}, 小路命中: {localRoadHits})");
        GD.Print($"   第二次查询: {secondRunTime}ms (主路命中: {mainRoadHits2}, 小路命中: {localRoadHits2})");
        GD.Print($"   缓存加速比: {(float)firstRunTime / Math.Max(secondRunTime, 1):F2}x");
        GD.Print($"   {roadSystem.GetStats()}");

        if (mainRoadHits == mainRoadHits2 && localRoadHits == localRoadHits2)
        {
            GD.Print("✅ 缓存性能测试通过！");
        }
        else
        {
            GD.PrintErr("❌ 缓存结果不一致！");
        }
    }

    /// <summary>
    /// 测试区域分割的道路相交检测
    /// </summary>
    public static void TestRoadIntersectionDetection()
    {
        GD.Print("🧪 开始测试区域分割的道路相交检测");

        var roadSystem = new OptimizedRoadSystem();

        // 创建测试区域
        var testArea = new Rect2I(100, 100, 200, 150);
        GD.Print($"   测试区域: {testArea}");

        // 测试1: 道路完全穿过区域（应该相交）
        GD.Print("📍 测试1: 道路完全穿过区域");
        var roadThroughArea = new OptimizedRoadSystem.RoadSegment
        {
            StartPoint = new Vector2I(50, 150),   // 区域左侧
            EndPoint = new Vector2I(350, 150),    // 区域右侧
            Width = 8,
            Type = OptimizedRoadSystem.RoadType.MainRoad
        };
        roadSystem.AddRoadSegment(roadThroughArea);

        // 测试2: 道路在区域旁边但不穿过（不应该相交）
        GD.Print("📍 测试2: 道路在区域旁边但不穿过");
        var roadBesideArea = new OptimizedRoadSystem.RoadSegment
        {
            StartPoint = new Vector2I(50, 80),    // 区域上方
            EndPoint = new Vector2I(350, 80),     // 区域上方
            Width = 8,
            Type = OptimizedRoadSystem.RoadType.MainRoad
        };
        roadSystem.AddRoadSegment(roadBesideArea);

        // 测试3: 道路一端在区域内（应该相交）
        GD.Print("📍 测试3: 道路一端在区域内");
        var roadPartiallyInArea = new OptimizedRoadSystem.RoadSegment
        {
            StartPoint = new Vector2I(150, 120),  // 区域内
            EndPoint = new Vector2I(350, 120),    // 区域外
            Width = 8,
            Type = OptimizedRoadSystem.RoadType.MainRoad
        };
        roadSystem.AddRoadSegment(roadPartiallyInArea);

        // 测试4: 道路完全在区域外且距离较远（不应该相交）
        GD.Print("📍 测试4: 道路完全在区域外且距离较远");
        var roadFarFromArea = new OptimizedRoadSystem.RoadSegment
        {
            StartPoint = new Vector2I(400, 200),  // 区域外
            EndPoint = new Vector2I(500, 200),    // 区域外
            Width = 8,
            Type = OptimizedRoadSystem.RoadType.MainRoad
        };
        roadSystem.AddRoadSegment(roadFarFromArea);

        // 创建测试区域对象
        var testRegion = new region_mes();
        testRegion.BuildingAreas.Add(testArea);

        // 执行分割前的状态
        GD.Print($"   分割前建筑区域数量: {testRegion.BuildingAreas.Count}");

        // 执行区域分割
        testRegion.SplitBuildingAreasByRoads(roadSystem);

        // 检查分割结果
        GD.Print($"   分割后建筑区域数量: {testRegion.BuildingAreas.Count}");

        // 分析结果
        if (testRegion.BuildingAreas.Count > 1)
        {
            GD.Print("✅ 区域被正确分割，说明检测到了穿过区域的道路");
            foreach (var area in testRegion.BuildingAreas)
            {
                GD.Print($"     分割后区域: {area}");
            }
        }
        else if (testRegion.BuildingAreas.Count == 1)
        {
            GD.Print("⚠️ 区域未被分割，可能是:");
            GD.Print("     1. 道路相交检测过于严格");
            GD.Print("     2. 道路确实没有穿过区域");
            GD.Print("     3. 分割逻辑有其他问题");
        }
        else
        {
            GD.PrintErr("❌ 异常：建筑区域数量为0");
        }
    }

    /// <summary>
    /// 测试边界情况的道路相交检测
    /// </summary>
    public static void TestBoundaryRoadIntersection()
    {
        GD.Print("🧪 开始测试边界情况的道路相交检测");

        // 创建一个测试区域
        var testArea = new Rect2I(100, 100, 100, 100); // 100x100的正方形区域
        GD.Print($"   测试区域: {testArea}");

        var testCases = new[]
        {
            new {
                Name = "道路紧贴区域上边界",
                Road = new { Start = new Vector2I(50, 95), End = new Vector2I(250, 95), Width = 8 },
                ShouldIntersect = false
            },
            new {
                Name = "道路紧贴区域下边界",
                Road = new { Start = new Vector2I(50, 205), End = new Vector2I(250, 205), Width = 8 },
                ShouldIntersect = false
            },
            new {
                Name = "道路紧贴区域左边界",
                Road = new { Start = new Vector2I(95, 50), End = new Vector2I(95, 250), Width = 8 },
                ShouldIntersect = false
            },
            new {
                Name = "道路紧贴区域右边界",
                Road = new { Start = new Vector2I(205, 50), End = new Vector2I(205, 250), Width = 8 },
                ShouldIntersect = false
            },
            new {
                Name = "道路穿过区域中心",
                Road = new { Start = new Vector2I(50, 150), End = new Vector2I(250, 150), Width = 8 },
                ShouldIntersect = true
            },
            new {
                Name = "道路刚好接触区域边界",
                Road = new { Start = new Vector2I(50, 100), End = new Vector2I(250, 100), Width = 8 },
                ShouldIntersect = true
            },
            new {
                Name = "道路在区域内部",
                Road = new { Start = new Vector2I(120, 120), End = new Vector2I(180, 180), Width = 8 },
                ShouldIntersect = true
            },
            new {
                Name = "道路距离区域很远",
                Road = new { Start = new Vector2I(300, 300), End = new Vector2I(400, 400), Width = 8 },
                ShouldIntersect = false
            }
        };

        foreach (var testCase in testCases)
        {
            GD.Print($"📍 测试: {testCase.Name}");

            var roadSystem = new OptimizedRoadSystem();
            var roadSegment = new OptimizedRoadSystem.RoadSegment
            {
                StartPoint = testCase.Road.Start,
                EndPoint = testCase.Road.End,
                Width = testCase.Road.Width,
                Type = OptimizedRoadSystem.RoadType.MainRoad
            };
            roadSystem.AddRoadSegment(roadSegment);

            var testRegion = new region_mes();
            testRegion.BuildingAreas.Add(testArea);

            int originalAreaCount = testRegion.BuildingAreas.Count;
            testRegion.SplitBuildingAreasByRoads(roadSystem);
            int newAreaCount = testRegion.BuildingAreas.Count;

            bool actuallyIntersected = newAreaCount > originalAreaCount;
            bool testPassed = actuallyIntersected == testCase.ShouldIntersect;

            string result = testPassed ? "✅ 通过" : "❌ 失败";
            GD.Print($"     期望相交: {testCase.ShouldIntersect}, 实际相交: {actuallyIntersected} - {result}");

            if (!testPassed)
            {
                GD.Print($"     道路: {testCase.Road.Start} -> {testCase.Road.End}, 宽度: {testCase.Road.Width}");
                GD.Print($"     区域: {testArea}");
            }
        }
    }
}
