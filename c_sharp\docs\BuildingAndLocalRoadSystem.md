# 面对面建筑和小路生成系统

## 概述

全新设计的面对面建筑系统为每个区域自动生成：
1. **面对面建筑布局**：建筑成对排列，中间是小路，形成自然的街道感
2. **智能方向选择**：根据区域长宽比确定最佳布局方向
3. **建筑块系统**：以"建筑-小路-建筑"为单位进行布局规划
4. **无依赖生成**：无论附近有无主路都会生成完整的建筑布局

## 核心设计理念

### 🏠 面对面建筑原则
- **建筑成对排列**：两排建筑面对面，中间是小路
- **明确朝向**：每个建筑都有明确的"正面"朝向小路
- **街道感**：形成自然的街道和社区布局

### 🧭 智能方向判断
- **区域更宽**：使用水平小路，建筑上下排列
  ```
  🏠🏠🏠 ← 上排朝下
  ═══════ ← 水平小路
  🏠🏠🏠 ← 下排朝上
  ```
- **区域更高**：使用垂直小路，建筑左右排列
  ```
  🏠 ║ 🏠
  🏠 ║ 🏠
  ↑  ↑  ↑
  左 路 右
  朝 　 朝
  右 　 左
  ```

### 🏗️ 建筑块系统
- **建筑块定义**：建筑 + 间距 + 小路 + 间距 + 建筑
- **块数计算**：根据区域大小计算能放置多少个完整建筑块
- **内部布局**：每个建筑块内建筑面对面排列

## 系统特性

### 🛣️ 小路生成
- **智能方向**：根据区域长宽比自动确定小路方向
- **均匀分布**：小路在区域内均匀分布
- **无依赖生成**：不依赖附近是否有主路
- **合理数量**：小路数量为建筑列数的一半

### 🏠 建筑放置
- **朝向小路**：建筑总是朝向最近的小路
- **填满区域**：尝试在整个区域内放置建筑
- **可调距离**：建筑到小路和建筑之间的距离可配置
- **冲突检测**：自动避免与小路和主路冲突

## 配置参数

```csharp
public class BuildingConfig
{
    public int LocalRoadWidth { get; set; } = 4;           // 小路宽度
    public int LocalRoadLength { get; set; } = 60;         // 小路长度
    public int BuildingToRoadDistance { get; set; } = 6;   // 建筑到小路的距离
    public int BuildingSpacing { get; set; } = 8;          // 建筑之间的距离
    public int MinDistanceToMainRoad { get; set; } = 12;   // 小路到主路的最小距离
    public RoomType DefaultRoomType { get; set; } = RoomType.BasicRoom; // 默认房间类型
}
```

### 参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `LocalRoadWidth` | 4 | 小路的宽度（网格单位） |
| `LocalRoadLength` | 60 | 小路的最大长度（网格单位） |
| `BuildingToRoadDistance` | 6 | 建筑到小路边缘的距离 |
| `BuildingSpacing` | 8 | 相邻建筑之间的间距 |
| `MinDistanceToMainRoad` | 12 | 小路起点到主路的最小距离 |
| `DefaultRoomType` | BasicRoom | 默认生成的房间类型 |

## 工作流程

### 1. 区域分析
```
区域尺寸 → 判断长宽比 → 确定小路方向 → 计算建筑布局
```

### 2. 小路生成
```
计算小路数量 → 均匀分布位置 → 生成小路 → 添加到道路系统
```

### 3. 建筑填充
```
网格化遍历 → 冲突检测 → 放置建筑 → 记录朝向信息
```

### 4. 详细流程
1. **区域分析**：判断区域是更宽还是更高
2. **计算布局**：确定能放置的建筑行列数
3. **小路规划**：小路数量 = 建筑列数 ÷ 2
4. **小路生成**：在区域内均匀分布小路
5. **建筑填充**：按网格遍历整个区域放置建筑
6. **冲突检测**：确保建筑不与小路和主路冲突

## 布局示例

### 更宽区域 → 水平小路布局（面对面）
```
区域边界 ┌─────────────────────────────────┐
        │ 🏠🏠🏠🏠🏠🏠🏠 ← 上排朝下      │
        │ ═══════════════ ← 水平小路      │
        │ 🏠🏠🏠🏠🏠🏠🏠 ← 下排朝上      │
        │                               │
        │ 🏠🏠🏠🏠🏠🏠🏠 ← 上排朝下      │
        │ ═══════════════ ← 水平小路      │
        │ 🏠🏠🏠🏠🏠🏠🏠 ← 下排朝上      │
        └─────────────────────────────────┘
建筑块数量 = 区域高度 / 建筑块高度
```

### 更高区域 → 垂直小路布局（面对面）
```
区域边界 ┌─────────────┐
        │ 🏠 ║ 🏠 ║ 🏠 │
        │ 🏠 ║ 🏠 ║ 🏠 │
        │ 🏠 ║ 🏠 ║ 🏠 │
        │ 🏠 ║ 🏠 ║ 🏠 │
        │ 🏠 ║ 🏠 ║ 🏠 │
        │ 🏠 ║ 🏠 ║ 🏠 │
        │ ↑  ↑  ↑  ↑  ↑ │
        │ 左 路 右 路 左 │
        │ 朝 　 朝 　 朝 │
        │ 右 　 左 　 右 │
        └─────────────┘
建筑块数量 = 区域宽度 / 建筑块宽度
```

## 使用方法

### 基本使用
系统会在 `creat_room()` 方法中自动调用：

```csharp
public void creat_room(region_mes city)
{
    // 自动为每个建筑区域生成建筑和小路
    foreach (var buildingArea in city.BuildingAreas)
    {
        GenerateBuildingsAndLocalRoads(buildingArea, city);
    }
}
```

### 自定义配置
```csharp
// 修改配置
buildingConfig.LocalRoadWidth = 6;        // 更宽的小路
buildingConfig.BuildingSpacing = 15;      // 更大的建筑间距
buildingConfig.DefaultRoomType = RoomType.LargeRoom; // 使用大房屋
```

## 技术实现

### 核心类

#### `LocalRoadSegment`
```csharp
public class LocalRoadSegment
{
    public Vector2I StartPoint { get; set; }
    public Vector2I EndPoint { get; set; }
    public int Width { get; set; } = 4;
    public bool IsHorizontal { get; set; }
}
```

#### `BuildingConfig`
配置所有建筑和小路生成的参数。

### 关键方法

1. **`FindNearbyMainRoads()`** - 找到区域附近的主路
2. **`GenerateLocalRoads()`** - 生成垂直于主路的小路
3. **`PlaceBuildingsAlongRoad()`** - 沿小路放置建筑
4. **`IsValidBuildingPosition()`** - 验证建筑位置是否有效

### 集成特性

- **道路系统集成**：小路自动添加到 `OptimizedRoadSystem`
- **房间系统集成**：建筑自动注册到 `SimpleRoomPreloadSystem`
- **地图显示集成**：小路和建筑在地图上正确显示

## 优势

1. **自动化**：无需手动放置建筑和小路
2. **智能布局**：建筑自动朝向最近的小路
3. **冲突避免**：自动检测并避免与主路冲突
4. **可配置**：所有参数都可以调整
5. **扩展性**：易于添加新的建筑类型和布局模式

## 调试信息

系统会输出详细的调试信息：

```
开始为区域 (500, 300) 生成建筑和小路
处理建筑区域: (450, 250, 100, 100)
  选择主路: 水平 从 (400, 200) 到 (600, 200)
  生成了 3 条小路
    小路已添加到系统: (470, 200) -> (470, 280) (宽度: 4)
    小路已添加到系统: (500, 200) -> (500, 280) (宽度: 4)
    小路已添加到系统: (530, 200) -> (530, 280) (宽度: 4)
    放置建筑: (464, 252) (朝向小路 X=470)
    放置建筑: (476, 252) (朝向小路 X=470)
    ...
```

现在你的城市将自动生成合理的建筑布局和小路网络！🏙️
