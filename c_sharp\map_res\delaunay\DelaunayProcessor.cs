using Godot;
using System;
using System.Collections.Generic;
using System.Linq;
using delaunay_algorithm;

/// <summary>
/// Delaunay三角剖分后处理器 - 去除超级三角形并生成正确的连接线和区域
/// </summary>
public partial class DelaunayProcessor : RefCounted
{
    /// <summary>
    /// 处理后的结果数据
    /// </summary>
    public class ProcessedResult
    {
        public List<Edge> ValidEdges { get; set; } = new(); // 有效的连接边
        public List<Edge> VoronoiEdges { get; set; } = new(); // Voronoi区域边界
        public List<DelaunayTriangle> ValidTriangles { get; set; } = new(); // 有效的三角形
        public List<VoronoiCell> VoronoiCells { get; set; } = new(); // Voronoi单元格
        public List<Site> OriginalSites { get; set; } = new(); // 原始输入点
    }

    /// <summary>
    /// Voronoi单元格
    /// </summary>
    public class VoronoiCell
    {
        public Site CenterSite { get; set; } // 中心点
        public List<Vector2> Vertices { get; set; } = new(); // 顶点列表
        public List<Edge> Edges { get; set; } = new(); // 边界边
        public float Area { get; set; } // 面积
        public List<Site> Neighbors { get; set; } = new(); // 邻居点
    }

    /// <summary>
    /// 处理Delaunay三角剖分结果，去除超级三角形并过滤质量差的区域
    /// </summary>
    public static ProcessedResult ProcessDelaunayResult(
        List<DelaunayTriangle> allTriangles,
        List<Site> originalSites,
        Site superTriangleA,
        Site superTriangleB,
        Site superTriangleC)
    {
        var result = new ProcessedResult();
        result.OriginalSites = new List<Site>(originalSites);

        // 1. 去除包含超级三角形顶点的三角形
        var validTriangles = RemoveSuperTriangles(allTriangles, superTriangleA, superTriangleB, superTriangleC);
        result.ValidTriangles = validTriangles;

        // 2. 生成初始Voronoi单元格
        var initialVoronoiCells = GenerateVoronoiCells(validTriangles, originalSites);

        // 3. 过滤质量差的区域
        var filteredCells = FilterLowQualityRegions(initialVoronoiCells);
        result.VoronoiCells = filteredCells;

        // 4. 根据过滤后的单元格重新生成有效的站点列表
        var filteredSites = filteredCells.Select(cell => cell.CenterSite).ToList();
        result.OriginalSites = filteredSites;

        // 5. 重新生成基于过滤后站点的边
        result.ValidEdges = ExtractValidEdges(validTriangles, filteredSites);
        result.VoronoiEdges = GenerateVoronoiEdges(validTriangles);

        GD.Print($"区域过滤结果: {originalSites.Count} -> {filteredSites.Count} (移除了 {originalSites.Count - filteredSites.Count} 个质量差的区域)");

        return result;
    }

    /// <summary>
    /// 去除包含超级三角形顶点的三角形
    /// </summary>
    private static List<DelaunayTriangle> RemoveSuperTriangles(
        List<DelaunayTriangle> allTriangles,
        Site superA, Site superB, Site superC)
    {
        var validTriangles = new List<DelaunayTriangle>();

        // GD.Print($"开始过滤超级三角形，总数: {allTriangles.Count}");
        // GD.Print($"超级三角形顶点:");
        // GD.Print($"  A: ({superA.x:F1}, {superA.y:F1})");
        // GD.Print($"  B: ({superB.x:F1}, {superB.y:F1})");
        // GD.Print($"  C: ({superC.x:F1}, {superC.y:F1})");

        foreach (var triangle in allTriangles)
        {
            // 检查三角形是否包含超级三角形的任何顶点
            bool site1IsSuper = IsSameSite(triangle.site1, superA) || IsSameSite(triangle.site1, superB) || IsSameSite(triangle.site1, superC);
            bool site2IsSuper = IsSameSite(triangle.site2, superA) || IsSameSite(triangle.site2, superB) || IsSameSite(triangle.site2, superC);
            bool site3IsSuper = IsSameSite(triangle.site3, superA) || IsSameSite(triangle.site3, superB) || IsSameSite(triangle.site3, superC);

            bool containsSuperVertex = site1IsSuper || site2IsSuper || site3IsSuper;

            if (!containsSuperVertex)
            {
                validTriangles.Add(triangle);
                // GD.Print($"  保留三角形: ({triangle.site1.x:F1},{triangle.site1.y:F1}), ({triangle.site2.x:F1},{triangle.site2.y:F1}), ({triangle.site3.x:F1},{triangle.site3.y:F1})");
            }
            else
            {
                string superInfo = "";
                if (site1IsSuper) superInfo += $"site1是超级顶点 ";
                if (site2IsSuper) superInfo += $"site2是超级顶点 ";
                if (site3IsSuper) superInfo += $"site3是超级顶点 ";

                // GD.Print($"  移除三角形: ({triangle.site1.x:F1},{triangle.site1.y:F1}), ({triangle.site2.x:F1},{triangle.site2.y:F1}), ({triangle.site3.x:F1},{triangle.site3.y:F1}) - {superInfo}");
            }
        }

        // GD.Print($"过滤完成: {allTriangles.Count} -> {validTriangles.Count}");

        // 检查是否有原始点失去了所有包含它们的三角形
        CheckOrphanedSites(validTriangles, allTriangles, superA, superB, superC);

        return validTriangles;
    }

    /// <summary>
    /// 过滤质量差的区域（面积太小或中心点不在区域内）
    /// </summary>
    private static List<VoronoiCell> FilterLowQualityRegions(List<VoronoiCell> voronoiCells)
    {
        var filteredCells = new List<VoronoiCell>();

        // 配置参数
        const float minAreaThreshold = 500.0f; // 最小面积阈值

        GD.Print($"开始过滤质量差的区域，总数: {voronoiCells.Count}");
        GD.Print($"过滤条件: 最小面积 >= {minAreaThreshold}，中心点必须在区域内");

        int removedSmallArea = 0;
        int removedCenterOutside = 0;

        foreach (var cell in voronoiCells)
        {
            bool shouldKeep = true;
            string removeReason = "";

            // 检查1: 面积是否太小
            if (cell.Area < minAreaThreshold)
            {
                shouldKeep = false;
                removeReason = $"面积太小 ({cell.Area:F1} < {minAreaThreshold})";
                removedSmallArea++;
            }
            // 检查2: 中心点是否在区域内
            else if (!IsPointInPolygon(new Vector2((float)cell.CenterSite.x, (float)cell.CenterSite.y), cell.Vertices))
            {
                shouldKeep = false;
                removeReason = $"中心点不在区域内";
                removedCenterOutside++;
            }

            if (shouldKeep)
            {
                filteredCells.Add(cell);
            }
            else
            {
                GD.Print($"  移除区域: 中心({cell.CenterSite.x:F1}, {cell.CenterSite.y:F1}), 面积={cell.Area:F1}, 原因: {removeReason}");
            }
        }

        GD.Print($"过滤完成: {voronoiCells.Count} -> {filteredCells.Count}");
        GD.Print($"  因面积太小移除: {removedSmallArea} 个");
        GD.Print($"  因中心点在外移除: {removedCenterOutside} 个");

        return filteredCells;
    }

    /// <summary>
    /// 检查点是否在多边形内（射线法）
    /// </summary>
    private static bool IsPointInPolygon(Vector2 point, List<Vector2> polygon)
    {
        if (polygon.Count < 3) return false;

        bool inside = false;
        int j = polygon.Count - 1;

        for (int i = 0; i < polygon.Count; i++)
        {
            Vector2 pi = polygon[i];
            Vector2 pj = polygon[j];

            if (((pi.Y > point.Y) != (pj.Y > point.Y)) &&
                (point.X < (pj.X - pi.X) * (point.Y - pi.Y) / (pj.Y - pi.Y) + pi.X))
            {
                inside = !inside;
            }
            j = i;
        }

        return inside;
    }

    /// <summary>
    /// 检查是否有原始点失去了所有包含它们的三角形
    /// </summary>
    private static void CheckOrphanedSites(List<DelaunayTriangle> validTriangles, List<DelaunayTriangle> allTriangles, Site superA, Site superB, Site superC)
    {
        // 从所有三角形中提取原始点（非超级三角形顶点）
        var allSites = new HashSet<Site>();
        foreach (var triangle in allTriangles)
        {
            if (!IsSameSite(triangle.site1, superA) && !IsSameSite(triangle.site1, superB) && !IsSameSite(triangle.site1, superC))
                allSites.Add(triangle.site1);
            if (!IsSameSite(triangle.site2, superA) && !IsSameSite(triangle.site2, superB) && !IsSameSite(triangle.site2, superC))
                allSites.Add(triangle.site2);
            if (!IsSameSite(triangle.site3, superA) && !IsSameSite(triangle.site3, superB) && !IsSameSite(triangle.site3, superC))
                allSites.Add(triangle.site3);
        }

        // GD.Print($"检查孤立点，总原始点数: {allSites.Count}");

        // 检查每个原始点是否在有效三角形中
        foreach (var site in allSites)
        {
            bool hasValidTriangle = false;
            foreach (var triangle in validTriangles)
            {
                if (IsSameSite(triangle.site1, site) || IsSameSite(triangle.site2, site) || IsSameSite(triangle.site3, site))
                {
                    hasValidTriangle = true;
                    break;
                }
            }

            if (!hasValidTriangle)
            {
                // GD.Print($"⚠️ 孤立点发现: ({site.x:F1}, {site.y:F1}) - 没有有效三角形包含此点");
            }
        }
    }

    /// <summary>
    /// 提取有效的Delaunay边（只包含原始输入点的边）
    /// </summary>
    private static List<Edge> ExtractValidEdges(List<DelaunayTriangle> validTriangles, List<Site> originalSites)
    {
        var validEdges = new List<Edge>();
        var edgeSet = new HashSet<string>(); // 用于去重

        GD.Print($"开始提取有效边，原始点数: {originalSites.Count}");

        foreach (var triangle in validTriangles)
        {
            // 检查三角形的每条边
            var edges = new[]
            {
                new Edge(triangle.site1, triangle.site2),
                new Edge(triangle.site1, triangle.site3),
                new Edge(triangle.site2, triangle.site3)
            };

            foreach (var edge in edges)
            {
                // 确保边的两个端点都在原始点集中（都是CenterSite）
                if (IsOriginalSite(edge.a, originalSites) && IsOriginalSite(edge.b, originalSites))
                {
                    string edgeKey = GetEdgeKey(edge);
                    if (!edgeSet.Contains(edgeKey))
                    {
                        validEdges.Add(edge);
                        edgeSet.Add(edgeKey);
                    }
                }
                else
                {
                    // 调试：输出被过滤的边
                    bool aIsOriginal = IsOriginalSite(edge.a, originalSites);
                    bool bIsOriginal = IsOriginalSite(edge.b, originalSites);

                    if (!aIsOriginal || !bIsOriginal)
                    {
                        GD.Print($"过滤边: ({edge.a.x:F1}, {edge.a.y:F1}) -> ({edge.b.x:F1}, {edge.b.y:F1}) " +
                                $"[A是原始点: {aIsOriginal}, B是原始点: {bIsOriginal}]");
                    }
                }
            }
        }

        GD.Print($"提取完成: {validEdges.Count} 条有效边（只连接CenterSite的边）");
        return validEdges;
    }




    /// <summary>
    /// 生成Voronoi边
    /// </summary>
    private static List<Edge> GenerateVoronoiEdges(List<DelaunayTriangle> validTriangles)
    {
        var voronoiEdges = new List<Edge>();
        var edgeSet = new HashSet<string>();

        // 为每对相邻的三角形创建Voronoi边
        for (int i = 0; i < validTriangles.Count; i++)
        {
            for (int j = i + 1; j < validTriangles.Count; j++)
            {
                var triangle1 = validTriangles[i];
                var triangle2 = validTriangles[j];

                // 检查两个三角形是否相邻（共享一条边）
                if (AreTrianglesAdjacent(triangle1, triangle2))
                {
                    // 创建连接两个三角形外心的边
                    var voronoiEdge = new Edge(triangle1.centerPoint, triangle2.centerPoint);
                    string edgeKey = GetEdgeKey(voronoiEdge);
                    
                    if (!edgeSet.Contains(edgeKey))
                    {
                        voronoiEdges.Add(voronoiEdge);
                        edgeSet.Add(edgeKey);
                    }
                }
            }
        }

        // GD.Print($"Generated {voronoiEdges.Count} Voronoi edges");
        return voronoiEdges;
    }

    /// <summary>
    /// 生成Voronoi单元格
    /// </summary>
    private static List<VoronoiCell> GenerateVoronoiCells(List<DelaunayTriangle> validTriangles, List<Site> originalSites)
    {
        var voronoiCells = new List<VoronoiCell>();

        // GD.Print($"开始生成Voronoi单元格，输入点数: {originalSites.Count}，有效三角形数: {validTriangles.Count}");

        foreach (var site in originalSites)
        {
            var cell = new VoronoiCell { CenterSite = site };

            // 找到包含此点的所有三角形
            var containingTriangles = validTriangles.Where(t =>
                IsSameSite(t.site1, site) || IsSameSite(t.site2, site) || IsSameSite(t.site3, site)).ToList();

            // GD.Print($"Site ({site.x:F1}, {site.y:F1}) has {containingTriangles.Count} containing triangles");

            if (containingTriangles.Count > 0)
            {
                // 收集外心作为Voronoi单元格的顶点
                var vertices = new List<Vector2>();
                foreach (var triangle in containingTriangles)
                {
                    // 检查外心是否有效
                    if (IsValidCircumcenter(triangle))
                    {
                        var vertex = new Vector2((float)triangle.centerPoint.x, (float)triangle.centerPoint.y);
                        vertices.Add(vertex);
                        // GD.Print($"  Valid triangle center: ({vertex.X:F1}, {vertex.Y:F1})");
                    }
                    else
                    {
                        // GD.Print($"  Invalid triangle center detected, skipping");
                    }
                }

                // 去除重复顶点
                vertices = RemoveDuplicateVertices(vertices);
                // GD.Print($"  After removing duplicates: {vertices.Count} vertices");

                if (vertices.Count >= 3)
                {
                    // 按角度排序顶点以形成正确的多边形
                    cell.Vertices = SortVerticesByAngle(vertices, new Vector2((float)site.x, (float)site.y));

                    // 调试：打印排序后的顶点
                    // GD.Print($"  Sorted vertices:");
                    // foreach (var v in cell.Vertices)
                    // {
                    //     GD.Print($"    ({v.X:F1}, {v.Y:F1})");
                    // }

                    // 计算面积
                    cell.Area = CalculatePolygonArea(cell.Vertices);
                    // GD.Print($"  Calculated area: {cell.Area:F2}");

                    // 找到邻居点
                    cell.Neighbors = FindNeighborSites(site, validTriangles, originalSites);

                    voronoiCells.Add(cell);
                }
                else
                {
                    // GD.Print($"  ⚠️ 顶点不足 ({vertices.Count}) 对于点 ({site.x:F1}, {site.y:F1})");
                }
            }
            else
            {
                // GD.Print($"  ⚠️ 没有包含三角形对于点 ({site.x:F1}, {site.y:F1})");
            }
        }

        GD.Print($"Voronoi单元格生成完成: 输入 {originalSites.Count} 个点，生成 {voronoiCells.Count} 个单元格");
        if (originalSites.Count != voronoiCells.Count)
        {
            // GD.PrintErr($"⚠️ Voronoi单元格数量不匹配！");

            // 找出哪些点没有生成单元格
            var generatedSites = voronoiCells.Select(c => c.CenterSite).ToHashSet();
            var missingSites = originalSites.Where(s => !generatedSites.Any(gs => IsSameSite(s, gs))).ToList();

            // if (missingSites.Count > 0)
            // {
            //     GD.Print($"缺失的点 ({missingSites.Count} 个):");
            //     foreach (var site in missingSites)
            //     {
            //         GD.Print($"  ({site.x:F1}, {site.y:F1})");
            //     }
            // }
        }

        return voronoiCells;
    }

    /// <summary>
    /// 检查三角形外心是否有效
    /// </summary>
    private static bool IsValidCircumcenter(DelaunayTriangle triangle)
    {
        // 检查外心坐标是否为有效数值
        if (double.IsNaN(triangle.centerPoint.x) || double.IsNaN(triangle.centerPoint.y) ||
            double.IsInfinity(triangle.centerPoint.x) || double.IsInfinity(triangle.centerPoint.y))
        {
            // GD.Print($"    Invalid circumcenter: NaN or Infinity detected");
            return false;
        }

        // 检查半径是否有效
        if (double.IsNaN(triangle.radius) || double.IsInfinity(triangle.radius) || triangle.radius <= 0)
        {
            // GD.Print($"    Invalid radius: {triangle.radius}");
            return false;
        }

        // 检查外心坐标是否在合理范围内（避免极大值）
        const double maxCoord = 1e6;
        if (Math.Abs(triangle.centerPoint.x) > maxCoord || Math.Abs(triangle.centerPoint.y) > maxCoord)
        {
            // GD.Print($"    Circumcenter too far: ({triangle.centerPoint.x:F1}, {triangle.centerPoint.y:F1})");
            return false;
        }

        return true;
    }

    /// <summary>
    /// 去除重复顶点
    /// </summary>
    private static List<Vector2> RemoveDuplicateVertices(List<Vector2> vertices)
    {
        var uniqueVertices = new List<Vector2>();
        const float tolerance = 1e-6f;

        foreach (var vertex in vertices)
        {
            bool isDuplicate = false;
            foreach (var existing in uniqueVertices)
            {
                if (Math.Abs(vertex.X - existing.X) < tolerance && Math.Abs(vertex.Y - existing.Y) < tolerance)
                {
                    isDuplicate = true;
                    break;
                }
            }

            if (!isDuplicate)
            {
                uniqueVertices.Add(vertex);
            }
        }

        return uniqueVertices;
    }

    /// <summary>
    /// 按角度排序顶点
    /// </summary>
    private static List<Vector2> SortVerticesByAngle(List<Vector2> vertices, Vector2 center)
    {
        return vertices.OrderBy(v => Mathf.Atan2(v.Y - center.Y, v.X - center.X)).ToList();
    }

    /// <summary>
    /// 计算多边形面积（使用鞋带公式）
    /// </summary>
    private static float CalculatePolygonArea(List<Vector2> vertices)
    {
        if (vertices.Count < 3)
        {
            // GD.Print($"    Area calculation: insufficient vertices ({vertices.Count})");
            return 0;
        }

        // 检查是否所有顶点都相同（退化情况）
        bool allSame = true;
        for (int i = 1; i < vertices.Count; i++)
        {
            if (Math.Abs(vertices[i].X - vertices[0].X) > 1e-6f ||
                Math.Abs(vertices[i].Y - vertices[0].Y) > 1e-6f)
            {
                allSame = false;
                break;
            }
        }

        if (allSame)
        {
            // GD.Print($"    All vertices are the same, area = 0");
            return 0;
        }

        // 使用鞋带公式计算面积
        float area = 0;
        // GD.Print($"    Area calculation for {vertices.Count} vertices:");

        for (int i = 0; i < vertices.Count; i++)
        {
            int j = (i + 1) % vertices.Count;
            float term1 = vertices[i].X * vertices[j].Y;
            float term2 = vertices[j].X * vertices[i].Y;
            float contribution = term1 - term2;
            area += contribution;
            // GD.Print($"      Edge {i}-{j}: {vertices[i].X:F1}*{vertices[j].Y:F1} - {vertices[j].X:F1}*{vertices[i].Y:F1} = {contribution:F2}");
        }

        float finalArea = Math.Abs(area) / 2.0f;
        //GD.Print($"    Shoelace formula: sum={area:F2}, final area={finalArea:F2}");

        // 额外验证：如果面积仍然为0，检查是否是共线点
        if (finalArea < 1e-6f)
        {
            //GD.Print($"    Warning: Very small area detected, checking for collinear points");
            bool isCollinear = CheckCollinear(vertices);
            if (isCollinear)
            {
                //GD.Print($"    Points are collinear, area correctly = 0");
            }
            else
            {
                //GD.Print($"    Points not collinear but area is tiny: {finalArea:E}");
            }
        }

        return finalArea;
    }

    /// <summary>
    /// 检查点是否共线
    /// </summary>
    private static bool CheckCollinear(List<Vector2> vertices)
    {
        if (vertices.Count < 3) return true;

        // 使用前三个不同的点检查共线性
        Vector2 p1 = vertices[0];
        Vector2 p2 = vertices[1];
        Vector2 p3 = vertices[2];

        // 计算叉积，如果为0则共线
        float crossProduct = (p2.X - p1.X) * (p3.Y - p1.Y) - (p2.Y - p1.Y) * (p3.X - p1.X);
        return Math.Abs(crossProduct) < 1e-6f;
    }

    /// <summary>
    /// 找到邻居点
    /// </summary>
    private static List<Site> FindNeighborSites(Site site, List<DelaunayTriangle> validTriangles, List<Site> originalSites)
    {
        var neighbors = new HashSet<Site>();

        foreach (var triangle in validTriangles)
        {
            if (IsSameSite(triangle.site1, site))
            {
                if (IsOriginalSite(triangle.site2, originalSites)) neighbors.Add(triangle.site2);
                if (IsOriginalSite(triangle.site3, originalSites)) neighbors.Add(triangle.site3);
            }
            else if (IsSameSite(triangle.site2, site))
            {
                if (IsOriginalSite(triangle.site1, originalSites)) neighbors.Add(triangle.site1);
                if (IsOriginalSite(triangle.site3, originalSites)) neighbors.Add(triangle.site3);
            }
            else if (IsSameSite(triangle.site3, site))
            {
                if (IsOriginalSite(triangle.site1, originalSites)) neighbors.Add(triangle.site1);
                if (IsOriginalSite(triangle.site2, originalSites)) neighbors.Add(triangle.site2);
            }
        }

        return neighbors.ToList();
    }

    /// <summary>
    /// 检查两个三角形是否相邻
    /// </summary>
    private static bool AreTrianglesAdjacent(DelaunayTriangle t1, DelaunayTriangle t2)
    {
        var sites1 = new[] { t1.site1, t1.site2, t1.site3 };
        var sites2 = new[] { t2.site1, t2.site2, t2.site3 };

        int sharedVertices = 0;
        foreach (var site1 in sites1)
        {
            foreach (var site2 in sites2)
            {
                if (IsSameSite(site1, site2))
                {
                    sharedVertices++;
                }
            }
        }

        return sharedVertices == 2; // 相邻三角形共享两个顶点（一条边）
    }

    /// <summary>
    /// 检查点是否在原始点集中
    /// </summary>
    private static bool IsOriginalSite(Site site, List<Site> originalSites)
    {
        return originalSites.Any(s => IsSameSite(s, site));
    }

    /// <summary>
    /// 检查两个点是否相同
    /// </summary>
    private static bool IsSameSite(Site a, Site b)
    {
        const double tolerance = 1e-6; // 增大容差，避免浮点数精度问题
        return Math.Abs(a.x - b.x) < tolerance && Math.Abs(a.y - b.y) < tolerance;
    }

    /// <summary>
    /// 获取边的唯一键（用于去重）
    /// </summary>
    private static string GetEdgeKey(Edge edge)
    {
        // 确保边的方向一致性
        var p1 = edge.a;
        var p2 = edge.b;
        
        if (p1.x > p2.x || (p1.x == p2.x && p1.y > p2.y))
        {
            (p1, p2) = (p2, p1);
        }
        
        return $"{p1.x:F6},{p1.y:F6}-{p2.x:F6},{p2.y:F6}";
    }

    /// <summary>
    /// 将结果转换为Godot的SegmentShape2D格式
    /// </summary>
    public static List<SegmentShape2D> ConvertToSegmentShapes(List<Edge> edges)
    {
        var segments = new List<SegmentShape2D>();
        
        foreach (var edge in edges)
        {
            var segment = new SegmentShape2D();
            segment.A = new Vector2((float)edge.a.x, (float)edge.a.y);
            segment.B = new Vector2((float)edge.b.x, (float)edge.b.y);
            segments.Add(segment);
        }
        
        return segments;
    }

    /// <summary>
    /// 获取区域的边界多边形
    /// </summary>
    public static Vector2[] GetRegionBoundary(VoronoiCell cell)
    {
        return cell.Vertices.ToArray();
    }
}
