using Godot;
using System;
using System.Collections.Generic;
using GameEnum;

/// <summary>
/// 建筑块类型枚举
/// </summary>
public enum BuildingBlockType
{
    Wall,       // 墙壁
    Door,       // 门
    Window,     // 窗户
    Furniture   // 家具
}

/// <summary>
/// 墙壁方向枚举
/// </summary>
public enum WallDirection
{
    North,  // 北墙
    South,  // 南墙
    East,   // 东墙
    West    // 西墙
}

/// <summary>
/// 建筑块基类 - 表示房屋中的各种构件
/// </summary>
public abstract class BuildingBlock
{
    /// <summary>
    /// 建筑块类型
    /// </summary>
    public BuildingBlockType Type { get; set; }
    
    /// <summary>
    /// 本地坐标位置
    /// </summary>
    public Vector2I LocalPosition { get; set; }
    
    /// <summary>
    /// 建筑块名称/描述
    /// </summary>
    public string Name { get; set; } = "";
    
    /// <summary>
    /// 是否可通行
    /// </summary>
    public bool IsPassable { get; set; } = false;
    
    /// <summary>
    /// 是否可交互
    /// </summary>
    public bool IsInteractable { get; set; } = false;

    protected BuildingBlock(BuildingBlockType type, Vector2I localPosition, string name = "")
    {
        Type = type;
        LocalPosition = localPosition;
        Name = name;
    }

    /// <summary>
    /// 获取世界坐标位置
    /// </summary>
    public virtual Vector2I GetWorldPosition(RoomBasic room)
    {
        return room.LocalToWorldPos(LocalPosition);
    }
}

/// <summary>
/// 墙壁类
/// </summary>
public class Wall : BuildingBlock
{
    /// <summary>
    /// 墙壁方向
    /// </summary>
    public WallDirection Direction { get; set; }
    
    /// <summary>
    /// 墙壁长度（占用的格子数）
    /// </summary>
    public int Length { get; set; } = 1;

    public Wall(Vector2I localPosition, WallDirection direction, int length = 1, string name = "墙壁") 
        : base(BuildingBlockType.Wall, localPosition, name)
    {
        Direction = direction;
        Length = length;
        IsPassable = false;
        IsInteractable = false;
    }

    /// <summary>
    /// 获取墙壁占用的所有坐标
    /// </summary>
    public List<Vector2I> GetOccupiedPositions()
    {
        var positions = new List<Vector2I>();
        
        for (int i = 0; i < Length; i++)
        {
            Vector2I offset = Direction switch
            {
                WallDirection.North => new Vector2I(i, 0),
                WallDirection.South => new Vector2I(i, 0),
                WallDirection.East => new Vector2I(0, i),
                WallDirection.West => new Vector2I(0, i),
                _ => Vector2I.Zero
            };
            
            positions.Add(LocalPosition + offset);
        }
        
        return positions;
    }
}

/// <summary>
/// 门类
/// </summary>
public class Door : BuildingBlock
{
    /// <summary>
    /// 门所在的墙壁方向
    /// </summary>
    public WallDirection WallDirection { get; set; }
    
    /// <summary>
    /// 门的宽度
    /// </summary>
    public int Width { get; set; } = 1;
    
    /// <summary>
    /// 是否开启
    /// </summary>
    public bool IsOpen { get; set; } = false;

    public Door(Vector2I localPosition, WallDirection wallDirection, int width = 1, string name = "门") 
        : base(BuildingBlockType.Door, localPosition, name)
    {
        WallDirection = wallDirection;
        Width = width;
        IsPassable = true;  // 门可以通行
        IsInteractable = true;  // 门可以交互
    }

    /// <summary>
    /// 获取门占用的所有坐标
    /// </summary>
    public List<Vector2I> GetOccupiedPositions()
    {
        var positions = new List<Vector2I>();
        
        for (int i = 0; i < Width; i++)
        {
            Vector2I offset = WallDirection switch
            {
                WallDirection.North => new Vector2I(i, 0),
                WallDirection.South => new Vector2I(i, 0),
                WallDirection.East => new Vector2I(0, i),
                WallDirection.West => new Vector2I(0, i),
                _ => Vector2I.Zero
            };
            
            positions.Add(LocalPosition + offset);
        }
        
        return positions;
    }
}

/// <summary>
/// 窗户类
/// </summary>
public class Window : BuildingBlock
{
    /// <summary>
    /// 窗户所在的墙壁方向
    /// </summary>
    public WallDirection WallDirection { get; set; }
    
    /// <summary>
    /// 窗户的宽度
    /// </summary>
    public int Width { get; set; } = 1;
    
    /// <summary>
    /// 是否开启
    /// </summary>
    public bool IsOpen { get; set; } = false;

    public Window(Vector2I localPosition, WallDirection wallDirection, int width = 1, string name = "窗户") 
        : base(BuildingBlockType.Window, localPosition, name)
    {
        WallDirection = wallDirection;
        Width = width;
        IsPassable = false;  // 窗户不可通行
        IsInteractable = true;  // 窗户可以交互（开关）
    }

    /// <summary>
    /// 获取窗户占用的所有坐标
    /// </summary>
    public List<Vector2I> GetOccupiedPositions()
    {
        var positions = new List<Vector2I>();
        
        for (int i = 0; i < Width; i++)
        {
            Vector2I offset = WallDirection switch
            {
                WallDirection.North => new Vector2I(i, 0),
                WallDirection.South => new Vector2I(i, 0),
                WallDirection.East => new Vector2I(0, i),
                WallDirection.West => new Vector2I(0, i),
                _ => Vector2I.Zero
            };
            
            positions.Add(LocalPosition + offset);
        }
        
        return positions;
    }
}



/// <summary>
/// 家具类
/// </summary>
public class Furniture : BuildingBlock
{
    /// <summary>
    /// 家具类型
    /// </summary>
    public string FurnitureType { get; set; } = "";
    
    /// <summary>
    /// 家具尺寸
    /// </summary>
    public Vector2I Size { get; set; } = Vector2I.One;
    
    /// <summary>
    /// 家具朝向
    /// </summary>
    public ORtation Orientation { get; set; } = ORtation.Down;

    public Furniture(Vector2I localPosition, string furnitureType, Vector2I size, 
                    ORtation orientation = ORtation.Down, string name = "家具") 
        : base(BuildingBlockType.Furniture, localPosition, name)
    {
        FurnitureType = furnitureType;
        Size = size;
        Orientation = orientation;
        IsPassable = false;  // 家具通常不可通行
        IsInteractable = true;  // 家具可以交互
    }

    /// <summary>
    /// 获取家具占用的所有坐标
    /// </summary>
    public List<Vector2I> GetOccupiedPositions()
    {
        var positions = new List<Vector2I>();
        
        for (int x = 0; x < Size.X; x++)
        {
            for (int y = 0; y < Size.Y; y++)
            {
                positions.Add(LocalPosition + new Vector2I(x, y));
            }
        }
        
        return positions;
    }
}
