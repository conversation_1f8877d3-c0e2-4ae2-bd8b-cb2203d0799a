using Godot;
using System;

/// <summary>
/// 技能范围显示器使用示例
/// </summary>
public partial class SkillRangeExample : Node2D
{
    // 技能范围显示器
    private SkillRangeDisplay skillRangeDisplay;
    
    // TileMapLayer引用（从您的地图系统获取）
    [Export]private TileMapLayer tileMapLayer;

    public override void _Ready()
    {
        // 创建技能范围显示器
        skillRangeDisplay = new SkillRangeDisplay();
        AddChild(skillRangeDisplay);
        
        // 这里需要从您的地图系统获取TileMapLayer
        // 例如：tileMapLayer = GetNode<TileMapLayer>("YourTileMapLayer");
        // 或者：tileMapLayer = yourMapSystem.GetTileMapLayer();
        
        // 初始化技能范围显示器
        if (tileMapLayer != null)
        {
            skillRangeDisplay.Initialize(tileMapLayer);
        }
        
        // 设置攻击范围样式
        skillRangeDisplay.SetAttackStyle();
    }

    public override void _Input(InputEvent @event)
    {
        if (@event is InputEventMouseButton mouseEvent && mouseEvent.Pressed)
        {
            if (mouseEvent.ButtonIndex == MouseButton.Left)
            {
                // 左键点击显示技能范围
                ShowSkillRangeAtMouse(mouseEvent.Position);
            }
            else if (mouseEvent.ButtonIndex == MouseButton.Right)
            {
                // 右键点击清除范围
                skillRangeDisplay.ClearRange();
            }
        }
        
        if (@event is InputEventKey keyEvent && keyEvent.Pressed)
        {
            switch (keyEvent.Keycode)
            {
                case Key.Key1:
                    // 数字键1：显示1x1范围
                    ShowSkillRangeAtCenter(1);
                    break;
                case Key.Key2:
                    // 数字键2：显示3x3范围
                    ShowSkillRangeAtCenter(3);
                    break;
                case Key.Key3:
                    // 数字键3：显示5x5范围
                    ShowSkillRangeAtCenter(5);
                    break;
                case Key.Key4:
                    // 数字键4：显示距离范围（网格距离）
                    ShowDistanceRangeAtCenter(4, true);
                    break;
                case Key.Key5:
                    // 数字键5：显示距离范围（直线距离，整体边界轮廓）
                    ShowDistanceRangeAtCenter(4, false);
                    break;
                case Key.Key6:
                    // 数字键6：显示视野范围
                    ShowViewRangeAtCenter(6);
                    break;
                case Key.Key7:
                    // 数字键7：显示扇形范围（向右，90度）
                    ShowSectorRangeAtCenter(4, 0, 90);
                    break;
                case Key.Key8:
                    // 数字键8：显示扇形范围（向上，120度）
                    ShowSectorRangeAtCenter(5, 90, 120);
                    break;
                case Key.Key9:
                    // 数字键9：显示扇形范围（向左，60度）
                    ShowSectorRangeAtCenter(3, 180, 60);
                    break;
                case Key.A:
                    // A键：切换到攻击样式
                    skillRangeDisplay.SetAttackStyle();
                    break;
                case Key.M:
                    // M键：切换到移动样式
                    skillRangeDisplay.SetMovementStyle();
                    break;
                case Key.H:
                    // H键：切换到治疗样式
                    skillRangeDisplay.SetHealStyle();
                    break;
                case Key.V:
                    // V键：切换到视野样式
                    skillRangeDisplay.SetViewStyle();
                    break;
                case Key.T:
                    // T键：切换到警戒样式
                    skillRangeDisplay.SetAlertStyle();
                    break;
                case Key.S:
                    // S键：切换到扇形样式
                    skillRangeDisplay.SetSectorStyle();
                    break;
                case Key.C:
                    // C键：清除范围
                    skillRangeDisplay.ClearRange();
                    break;
            }
        }
    }

    /// <summary>
    /// 在鼠标位置显示距离范围
    /// </summary>
    private void ShowSkillRangeAtMouse(Vector2 mousePosition)
    {
        if (tileMapLayer == null) return;

        // 将鼠标位置转换为网格坐标
        Vector2I gridCoord = tileMapLayer.LocalToMap(mousePosition);

        // 显示距离为3的范围（网格距离，整体边界）
        skillRangeDisplay.SetDistanceStyle();
        skillRangeDisplay.ShowDistanceRange(gridCoord, 3, true);

        GD.Print($"在网格坐标 ({gridCoord.X}, {gridCoord.Y}) 显示距离范围");
    }

    /// <summary>
    /// 在地图中心显示技能范围
    /// </summary>
    private void ShowSkillRangeAtCenter(int sideLength)
    {
        // 在地图中心显示范围
        Vector2I centerCoord = new Vector2I(0, 0);
        skillRangeDisplay.ShowSquareRange(centerCoord, sideLength);

        GD.Print($"在中心显示 {sideLength}x{sideLength} 技能范围");
    }

    /// <summary>
    /// 在地图中心显示距离范围
    /// </summary>
    private void ShowDistanceRangeAtCenter(int distance, bool useGridDistance)
    {
        Vector2I centerCoord = new Vector2I(0, 0);

        skillRangeDisplay.SetDistanceStyle();
        skillRangeDisplay.ShowDistanceRange(centerCoord, distance, useGridDistance);

        string distanceType = useGridDistance ? "网格距离" : "直线距离";
        GD.Print($"在中心显示{distanceType} {distance} 的范围");
    }



    /// <summary>
    /// 在地图中心显示视野范围
    /// </summary>
    private void ShowViewRangeAtCenter(int viewRadius)
    {
        Vector2I centerCoord = new Vector2I(0, 0);

        // 简单的障碍物检查函数示例
        Func<Vector2I, bool> obstacleChecker = (Vector2I pos) =>
        {
            // 示例：假设某些位置有障碍物
            return (pos.X % 5 == 0 && pos.Y % 5 == 0); // 每5格有一个障碍物
        };

        skillRangeDisplay.SetViewStyle();
        skillRangeDisplay.ShowViewRange(centerCoord, viewRadius, obstacleChecker);

        GD.Print($"在中心显示视野半径 {viewRadius} 的范围");
    }

    /// <summary>
    /// 在地图中心显示扇形范围
    /// </summary>
    private void ShowSectorRangeAtCenter(int range, float direction, float angle)
    {
        Vector2I centerCoord = new Vector2I(0, 0);

        skillRangeDisplay.SetSectorStyle();
        skillRangeDisplay.ShowSectorRange(centerCoord, range, direction, angle, true);

        GD.Print($"在中心显示扇形范围：半径{range}，方向{direction}°，角度{angle}°");
    }
}
