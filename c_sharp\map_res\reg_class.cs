using Godot;
using System;
using GameEnum;
using delaunay_algorithm;
using System.Collections.Generic;
using Godot.NativeInterop;
using System.Linq;

public partial class region_mes
{
    public string region_name { get; set; } = "未命名";// 城市名称
    public Region_type region_type { get; set; } = Region_type.City;// 城市类型
    public Vector2I central_coord { get; set; } // 中心坐标
    public List<Vector2> BoundaryVertices { get; set; } = new(); // 边界顶点（多边形）

    /// <summary>
    /// 建筑区域 - 比边界围成的区域小约50%的矩形区域，用于放置建筑
    /// </summary>
    public List<Rect2I> BuildingAreas { get; set; } = new List<Rect2I>();

    // 连接信息
    public List<Vector2I> ConnectedPoints { get; set; } = new();     // 直接连接的邻居点位
    public Dictionary<Vector2I, Edge> ConnectionEdges { get; set; } = new(); // 连接边信息
    public List<Vector2I> RoadPaths { get; set; } = new();          // 道路路径点

    /// <summary>
    /// 根据边界顶点计算建筑区域（支持道路分割）
    /// </summary>
    public void CalculateBuildingArea()
    {
        BuildingAreas.Clear();

        if (BoundaryVertices.Count < 3)
        {
            return;
        }

        // 计算多边形的内接矩形
        var inscribedRect = FindLargestInscribedRectangle();

        // 缩小到约50%（使用0.7倍系数，面积约为原来的49%）
        float scaleFactor = 0.8f;
        float newWidth = inscribedRect.Size.X * scaleFactor;
        float newHeight = inscribedRect.Size.Y * scaleFactor;

        // 保持在内接矩形的中心
        Vector2 center = inscribedRect.GetCenter();
        int buildingX = (int)(center.X - newWidth / 2);
        int buildingY = (int)(center.Y - newHeight / 2);
        int buildingWidth = (int)newWidth;
        int buildingHeight = (int)newHeight;

        var initialArea = new Rect2I(buildingX, buildingY, buildingWidth, buildingHeight);

        // 暂时先添加单个区域，后续会根据道路进行分割
        BuildingAreas.Add(initialArea);
    }

    /// <summary>
    /// 寻找多边形的最大内接矩形（简化算法）
    /// </summary>
    private Rect2 FindLargestInscribedRectangle()
    {
        // 计算多边形的包围盒作为搜索范围
        float minX = BoundaryVertices.Min(v => v.X);
        float maxX = BoundaryVertices.Max(v => v.X);
        float minY = BoundaryVertices.Min(v => v.Y);
        float maxY = BoundaryVertices.Max(v => v.Y);

        float bestArea = 0;
        Rect2 bestRect = new Rect2();

        // 使用网格搜索找到最大内接矩形
        int gridSize = 20; // 搜索精度
        float stepX = (maxX - minX) / gridSize;
        float stepY = (maxY - minY) / gridSize;

        for (float x1 = minX; x1 < maxX; x1 += stepX)
        {
            for (float y1 = minY; y1 < maxY; y1 += stepY)
            {
                for (float x2 = x1 + stepX; x2 <= maxX; x2 += stepX)
                {
                    for (float y2 = y1 + stepY; y2 <= maxY; y2 += stepY)
                    {
                        var rect = new Rect2(x1, y1, x2 - x1, y2 - y1);

                        // 检查矩形是否完全在多边形内
                        if (IsRectangleInsidePolygon(rect))
                        {
                            // 直接计算面积避免方法调用开销
                            float area = rect.Size.X * rect.Size.Y;
                            if (area > bestArea)
                            {
                                bestArea = area;
                                bestRect = rect;
                            }
                        }
                    }
                }
            }
        }

        // 如果没有找到合适的内接矩形，使用中心的小矩形
        if (bestArea == 0)
        {
            Vector2 center = new Vector2((minX + maxX) / 2, (minY + maxY) / 2);
            float size = Math.Min(maxX - minX, maxY - minY) * 0.3f;
            bestRect = new Rect2(center.X - size / 2, center.Y - size / 2, size, size);
        }

        return bestRect;
    }

    /// <summary>
    /// 检查矩形是否完全在多边形内部
    /// </summary>
    private bool IsRectangleInsidePolygon(Rect2 rect)
    {
        // 检查矩形的四个角是否都在多边形内
        Vector2[] corners = {
            rect.Position,                                    // 左上
            new Vector2(rect.End.X, rect.Position.Y),        // 右上
            rect.End,                                         // 右下
            new Vector2(rect.Position.X, rect.End.Y)         // 左下
        };

        foreach (var corner in corners)
        {
            if (!IsPointInPolygon(corner))
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 使用射线法判断点是否在多边形内
    /// </summary>
    private bool IsPointInPolygon(Vector2 point)
    {
        int intersections = 0;
        int vertexCount = BoundaryVertices.Count;

        for (int i = 0; i < vertexCount; i++)
        {
            Vector2 v1 = BoundaryVertices[i];
            Vector2 v2 = BoundaryVertices[(i + 1) % vertexCount];

            // 检查射线与边的交点
            if (((v1.Y > point.Y) != (v2.Y > point.Y)) &&
                (point.X < (v2.X - v1.X) * (point.Y - v1.Y) / (v2.Y - v1.Y) + v1.X))
            {
                intersections++;
            }
        }

        return (intersections % 2) == 1;
    }

    /// <summary>
    /// 检查指定坐标是否在任一建筑区域内
    /// </summary>
    /// <param name="position">要检查的坐标</param>
    /// <returns>是否在建筑区域内</returns>
    public bool IsInBuildingArea(Vector2I position)
    {
        return BuildingAreas.Any(area => area.HasPoint(position));
    }

    /// <summary>
    /// 获取第一个建筑区域的中心点（兼容性方法）
    /// </summary>
    /// <returns>建筑区域中心点</returns>
    public Vector2I GetBuildingAreaCenter()
    {
        if (BuildingAreas.Count > 0)
            return BuildingAreas[0].GetCenter();
        return new Vector2I();
    }

    /// <summary>
    /// 获取所有建筑区域的总面积
    /// </summary>
    /// <returns>建筑区域总面积</returns>
    public int GetBuildingAreaSize()
    {
        return BuildingAreas.Sum(area => area.Size.X * area.Size.Y);
    }

    /// <summary>
    /// 获取所有建筑区域的中心点列表
    /// </summary>
    /// <returns>建筑区域中心点列表</returns>
    public List<Vector2I> GetAllBuildingAreaCenters()
    {
        return BuildingAreas.Select(area => area.GetCenter()).ToList();
    }

    /// <summary>
    /// 根据主道路分割建筑区域
    /// </summary>
    /// <param name="roadSystem">道路系统</param>
    public void SplitBuildingAreasByRoads(OptimizedRoadSystem roadSystem)
    {
        if (BuildingAreas.Count == 0) return;

        var newBuildingAreas = new List<Rect2I>();

        foreach (var area in BuildingAreas)
        {
            var splitAreas = SplitAreaByRoads(area, roadSystem);
            newBuildingAreas.AddRange(splitAreas);
        }

        BuildingAreas = newBuildingAreas;
    }

    /// <summary>
    /// 将单个区域按道路分割（智能分割版本）
    /// </summary>
    /// <param name="area">要分割的区域</param>
    /// <param name="roadSystem">道路系统</param>
    /// <returns>分割后的区域列表</returns>
    private List<Rect2I> SplitAreaByRoads(Rect2I area, OptimizedRoadSystem roadSystem)
    {
        var result = new List<Rect2I>();

        // 获取穿过区域的道路线段
        var intersectingSegments = GetIntersectingRoadSegments(area, roadSystem);

        if (intersectingSegments.Count == 0)
        {
            // 没有道路穿过，保持原区域
            result.Add(area);
            return result;
        }

        // 根据道路数量和方向智能分割
        var splitResult = IntelligentSplit(area, intersectingSegments);

        // 过滤掉太小的区域
        foreach (var splitArea in splitResult)
        {
            if (splitArea.Size.X >= 40 && splitArea.Size.Y >= 40)
            {
                result.Add(splitArea);
            }
        }

        // 如果分割后没有有效区域，保持原区域
        if (result.Count == 0)
        {
            result.Add(area);
        }
        else
        {
        }

        return result;
    }

    /// <summary>
    /// 获取穿过区域的道路线段（只考虑主路进行分割）
    /// </summary>
    private List<OptimizedRoadSystem.RoadSegment> GetIntersectingRoadSegments(Rect2I area, OptimizedRoadSystem roadSystem)
    {
        var intersectingSegments = new List<OptimizedRoadSystem.RoadSegment>();
        // 只获取主路进行区域分割，小路不参与分割
        var roadSegments = roadSystem.GetMainRoadSegments();

        foreach (var segment in roadSegments)
        {
            bool intersects = DoesSegmentIntersectArea(segment, area);
            if (intersects)
            {
                intersectingSegments.Add(segment);
            }
        }

        return intersectingSegments;
    }

    /// <summary>
    /// 智能分割区域（支持复杂道路网络）
    /// </summary>
    private List<Rect2I> IntelligentSplit(Rect2I area, List<OptimizedRoadSystem.RoadSegment> segments)
    {
        // 计算统一的道路半宽（与DoesSegmentIntersectArea保持一致）
        int roadHalfWidth = segments.Count > 0 ?
            Math.Max(1, segments[0].Width / 2 +2) : 0;

        // GD.Print($"    使用道路半宽: {roadHalfWidth} (原始宽度: {(segments.Count > 0 ? segments[0].Width : 0)})");

        // 分析道路模式
        var roadPattern = AnalyzeRoadPattern(area, segments, roadHalfWidth);

        // 根据道路模式选择分割策略
        if (roadPattern.HorizontalLines.Count > 0 || roadPattern.VerticalLines.Count > 0)
        {
            // 使用网格分割
            return GridBasedSplit(area, roadPattern, roadHalfWidth);
        }
        else if (roadPattern.DiagonalSegments.Count > 0)
        {
            // 斜线道路，简单四象限分割
            return SplitIntoQuadrants(area);
        }
        else
        {
            // 没有道路，保持原区域
            return new List<Rect2I> { area };
        }
    }

    /// <summary>
    /// 分析道路模式（简化版：恢复原有逻辑）
    /// </summary>
    private RoadPattern AnalyzeRoadPattern(Rect2I area, List<OptimizedRoadSystem.RoadSegment> segments, int roadHalfWidth)
    {
        var pattern = new RoadPattern();

        foreach (var segment in segments)
        {
            if (segment.IsHorizontal)
            {
                // 水平道路：记录Y坐标
                int roadY = segment.StartPoint.Y;
                if (roadY >= area.Position.Y && roadY <= area.Position.Y + area.Size.Y)
                {
                    if (!pattern.HorizontalLines.Contains(roadY))
                    {
                        pattern.HorizontalLines.Add(roadY);
                    }
                }
            }
            else if (segment.IsVertical)
            {
                // 垂直道路：记录X坐标
                int roadX = segment.StartPoint.X;
                if (roadX >= area.Position.X && roadX <= area.Position.X + area.Size.X)
                {
                    if (!pattern.VerticalLines.Contains(roadX))
                    {
                        pattern.VerticalLines.Add(roadX);
                    }
                }
            }
            else
            {
                // 斜线道路
                pattern.DiagonalSegments.Add(segment);
            }
        }

        // 排序道路线
        pattern.HorizontalLines.Sort();
        pattern.VerticalLines.Sort();

        return pattern;
    }

    /// <summary>
    /// 基于道路拓扑的智能分割（正确处理T型道路）
    /// </summary>
    private List<Rect2I> GridBasedSplit(Rect2I area, RoadPattern pattern, int roadHalfWidth)
    {
        var result = new List<Rect2I>();

        // 如果只有一条道路，使用简单分割
        if (pattern.HorizontalLines.Count + pattern.VerticalLines.Count == 1)
        {
            return SimpleSingleRoadSplit(area, pattern, roadHalfWidth);
        }

        // 对于多条道路，使用拓扑感知分割
        return TopologyAwareSplit(area, pattern, roadHalfWidth);
    }

    /// <summary>
    /// 单条道路的简单分割
    /// </summary>
    private List<Rect2I> SimpleSingleRoadSplit(Rect2I area, RoadPattern pattern, int roadHalfWidth)
    {
        var result = new List<Rect2I>();

        if (pattern.HorizontalLines.Count == 1)
        {
            // 水平道路分割
            int roadY = pattern.HorizontalLines[0];
            if (roadY > area.Position.Y && roadY < area.Position.Y + area.Size.Y)
            {
                // 上半部分
                var upperArea = new Rect2I(
                    area.Position.X,
                    area.Position.Y,
                    area.Size.X,
                    roadY - area.Position.Y - roadHalfWidth
                );

                // 下半部分
                var lowerArea = new Rect2I(
                    area.Position.X,
                    roadY + roadHalfWidth,
                    area.Size.X,
                    area.Position.Y + area.Size.Y - (roadY + roadHalfWidth)
                );

                if (upperArea.Size.Y > 10) result.Add(upperArea);
                if (lowerArea.Size.Y > 10) result.Add(lowerArea);
            }
        }
        else if (pattern.VerticalLines.Count == 1)
        {
            // 垂直道路分割
            int roadX = pattern.VerticalLines[0];
            if (roadX > area.Position.X && roadX < area.Position.X + area.Size.X)
            {
                // 左半部分
                var leftArea = new Rect2I(
                    area.Position.X,
                    area.Position.Y,
                    roadX - area.Position.X - roadHalfWidth,
                    area.Size.Y
                );

                // 右半部分
                var rightArea = new Rect2I(
                    roadX + roadHalfWidth,
                    area.Position.Y,
                    area.Position.X + area.Size.X - (roadX + roadHalfWidth),
                    area.Size.Y
                );

                if (leftArea.Size.X > 10) result.Add(leftArea);
                if (rightArea.Size.X > 10) result.Add(rightArea);
            }
        }

        return result.Count > 0 ? result : new List<Rect2I> { area };
    }

    /// <summary>
    /// 拓扑感知的分割（正确处理T型和十字型道路）
    /// </summary>
    private List<Rect2I> TopologyAwareSplit(Rect2I area, RoadPattern pattern, int roadHalfWidth)
    {
        var result = new List<Rect2I>();

        // 分析道路连接关系
        var roadConnections = AnalyzeRoadConnections(area, pattern);

        // 根据连接关系进行分割
        if (IsTPatterRoad(roadConnections))
        {
            result = SplitForTPattern(area, roadConnections, roadHalfWidth);
        }
        else if (IsCrossPattern(roadConnections))
        {
            result = SplitForCrossPattern(area, roadConnections, roadHalfWidth);
        }
        else
        {
            // 回退到简单网格分割
            result = FallbackGridSplit(area, pattern, roadHalfWidth);
        }

        GD.Print($"    拓扑感知分割完成: 生成 {result.Count} 个建筑区域");
        return result;
    }

    /// <summary>
    /// 道路连接信息
    /// </summary>
    private class RoadConnections
    {
        public List<int> HorizontalRoads { get; set; } = new List<int>();
        public List<int> VerticalRoads { get; set; } = new List<int>();
        public List<Vector2I> Intersections { get; set; } = new List<Vector2I>();
    }

    /// <summary>
    /// 分析道路连接关系
    /// </summary>
    private RoadConnections AnalyzeRoadConnections(Rect2I area, RoadPattern pattern)
    {
        var connections = new RoadConnections();

        // 筛选在区域内的道路
        foreach (var roadY in pattern.HorizontalLines)
        {
            if (roadY > area.Position.Y && roadY < area.Position.Y + area.Size.Y)
            {
                connections.HorizontalRoads.Add(roadY);
            }
        }

        foreach (var roadX in pattern.VerticalLines)
        {
            if (roadX > area.Position.X && roadX < area.Position.X + area.Size.X)
            {
                connections.VerticalRoads.Add(roadX);
            }
        }

        // 找到交叉点
        foreach (var roadY in connections.HorizontalRoads)
        {
            foreach (var roadX in connections.VerticalRoads)
            {
                connections.Intersections.Add(new Vector2I(roadX, roadY));
            }
        }

        return connections;
    }

    /// <summary>
    /// 判断是否为T型道路模式
    /// </summary>
    private bool IsTPatterRoad(RoadConnections connections)
    {
        // T型道路：一条主干道 + 一条分支道路
        return (connections.HorizontalRoads.Count == 1 && connections.VerticalRoads.Count == 1) ||
               (connections.HorizontalRoads.Count == 2 && connections.VerticalRoads.Count == 1) ||
               (connections.HorizontalRoads.Count == 1 && connections.VerticalRoads.Count == 2);
    }

    /// <summary>
    /// 判断是否为十字型道路模式
    /// </summary>
    private bool IsCrossPattern(RoadConnections connections)
    {
        // 十字型：至少一条水平道路和一条垂直道路相交
        return connections.HorizontalRoads.Count >= 1 && connections.VerticalRoads.Count >= 1 &&
               connections.Intersections.Count >= 1;
    }

    /// <summary>
    /// T型道路分割
    /// </summary>
    private List<Rect2I> SplitForTPattern(Rect2I area, RoadConnections connections, int roadHalfWidth)
    {
        var result = new List<Rect2I>();

        // 确定主干道和分支道路
        if (connections.HorizontalRoads.Count == 1 && connections.VerticalRoads.Count == 1)
        {
            // 标准T型：一条水平道路 + 一条垂直道路
            int roadY = connections.HorizontalRoads[0];
            int roadX = connections.VerticalRoads[0];

            // 创建3个区域
            // 1. 左上区域
            var leftUpper = new Rect2I(
                area.Position.X,
                area.Position.Y,
                roadX - area.Position.X - roadHalfWidth,
                roadY - area.Position.Y - roadHalfWidth
            );

            // 2. 右上区域
            var rightUpper = new Rect2I(
                roadX + roadHalfWidth,
                area.Position.Y,
                area.Position.X + area.Size.X - (roadX + roadHalfWidth),
                roadY - area.Position.Y - roadHalfWidth
            );

            // 3. 下方区域（跨越整个宽度）
            var lower = new Rect2I(
                area.Position.X,
                roadY + roadHalfWidth,
                area.Size.X,
                area.Position.Y + area.Size.Y - (roadY + roadHalfWidth)
            );

            // 添加有效区域
            if (leftUpper.Size.X > 10 && leftUpper.Size.Y > 10) result.Add(leftUpper);
            if (rightUpper.Size.X > 10 && rightUpper.Size.Y > 10) result.Add(rightUpper);
            if (lower.Size.X > 10 && lower.Size.Y > 10) result.Add(lower);
        }
        else
        {
            // 复杂T型，回退到网格分割
            result = FallbackGridSplit(area, new RoadPattern
            {
                HorizontalLines = connections.HorizontalRoads,
                VerticalLines = connections.VerticalRoads
            }, roadHalfWidth);
        }

        return result.Count > 0 ? result : new List<Rect2I> { area };
    }

    /// <summary>
    /// 十字型道路分割
    /// </summary>
    private List<Rect2I> SplitForCrossPattern(Rect2I area, RoadConnections connections, int roadHalfWidth)
    {
        var result = new List<Rect2I>();

        if (connections.HorizontalRoads.Count == 1 && connections.VerticalRoads.Count == 1)
        {
            // 标准十字型：一条水平道路 + 一条垂直道路
            int roadY = connections.HorizontalRoads[0];
            int roadX = connections.VerticalRoads[0];

            // 创建4个区域
            // 1. 左上区域
            var leftUpper = new Rect2I(
                area.Position.X,
                area.Position.Y,
                roadX - area.Position.X - roadHalfWidth,
                roadY - area.Position.Y - roadHalfWidth
            );

            // 2. 右上区域
            var rightUpper = new Rect2I(
                roadX + roadHalfWidth,
                area.Position.Y,
                area.Position.X + area.Size.X - (roadX + roadHalfWidth),
                roadY - area.Position.Y - roadHalfWidth
            );

            // 3. 左下区域
            var leftLower = new Rect2I(
                area.Position.X,
                roadY + roadHalfWidth,
                roadX - area.Position.X - roadHalfWidth,
                area.Position.Y + area.Size.Y - (roadY + roadHalfWidth)
            );

            // 4. 右下区域
            var rightLower = new Rect2I(
                roadX + roadHalfWidth,
                roadY + roadHalfWidth,
                area.Position.X + area.Size.X - (roadX + roadHalfWidth),
                area.Position.Y + area.Size.Y - (roadY + roadHalfWidth)
            );

            // 添加有效区域
            if (leftUpper.Size.X > 10 && leftUpper.Size.Y > 10) result.Add(leftUpper);
            if (rightUpper.Size.X > 10 && rightUpper.Size.Y > 10) result.Add(rightUpper);
            if (leftLower.Size.X > 10 && leftLower.Size.Y > 10) result.Add(leftLower);
            if (rightLower.Size.X > 10 && rightLower.Size.Y > 10) result.Add(rightLower);
        }
        else
        {
            // 复杂十字型，回退到网格分割
            result = FallbackGridSplit(area, new RoadPattern
            {
                HorizontalLines = connections.HorizontalRoads,
                VerticalLines = connections.VerticalRoads
            }, roadHalfWidth);
        }

        return result.Count > 0 ? result : new List<Rect2I> { area };
    }

    /// <summary>
    /// 回退的网格分割方法（用于复杂情况）
    /// </summary>
    private List<Rect2I> FallbackGridSplit(Rect2I area, RoadPattern pattern, int roadHalfWidth)
    {
        var result = new List<Rect2I>();

        // 构建分割线
        var xSplits = new List<int> { area.Position.X };
        var ySplits = new List<int> { area.Position.Y };

        // 添加垂直道路作为X分割线
        foreach (var roadX in pattern.VerticalLines)
        {
            if (roadX > area.Position.X && roadX < area.Position.X + area.Size.X)
            {
                xSplits.Add(roadX);
            }
        }

        // 添加水平道路作为Y分割线
        foreach (var roadY in pattern.HorizontalLines)
        {
            if (roadY > area.Position.Y && roadY < area.Position.Y + area.Size.Y)
            {
                ySplits.Add(roadY);
            }
        }

        // 添加结束边界
        xSplits.Add(area.Position.X + area.Size.X);
        ySplits.Add(area.Position.Y + area.Size.Y);

        // 排序并去重
        xSplits = xSplits.Distinct().OrderBy(x => x).ToList();
        ySplits = ySplits.Distinct().OrderBy(y => y).ToList();

        // 生成网格区域
        for (int i = 0; i < xSplits.Count - 1; i++)
        {
            for (int j = 0; j < ySplits.Count - 1; j++)
            {
                int x = xSplits[i];
                int y = ySplits[j];
                int width = xSplits[i + 1] - x;
                int height = ySplits[j + 1] - y;

                if (width > 0 && height > 0)
                {
                    // 调整区域边界，避开道路宽度
                    var adjustedArea = AdjustAreaToAvoidRoads(new Rect2I(x, y, width, height), pattern, roadHalfWidth);

                    if (adjustedArea.Size.X > 10 && adjustedArea.Size.Y > 10)
                    {
                        result.Add(adjustedArea);
                    }
                }
            }
        }

        return result;
    }

    /// <summary>
    /// 调整区域边界以避开道路（简化版：直接处理道路中心线分割的情况）
    /// </summary>
    private Rect2I AdjustAreaToAvoidRoads(Rect2I area, RoadPattern pattern, int roadHalfWidth)
    {
        int newX = area.Position.X;
        int newY = area.Position.Y;
        int newWidth = area.Size.X;
        int newHeight = area.Size.Y;

        // 检查垂直道路（影响左右边界）
        foreach (var roadX in pattern.VerticalLines)
        {
            // 如果左边界在道路中心线上，向右移动
            if (area.Position.X == roadX)
            {
                int adjustment = roadHalfWidth + 1;
                newX += adjustment;
                newWidth -= adjustment;
            }

            // 如果右边界在道路中心线上，向左收缩
            if (area.Position.X + area.Size.X == roadX)
            {
                int adjustment = roadHalfWidth + 1;
                newWidth -= adjustment;
            }
        }

        // 检查水平道路（影响上下边界）
        foreach (var roadY in pattern.HorizontalLines)
        {
            // 如果上边界在道路中心线上，向下移动
            if (area.Position.Y == roadY)
            {
                int adjustment = roadHalfWidth + 1;
                newY += adjustment;
                newHeight -= adjustment;
            }

            // 如果下边界在道路中心线上，向上收缩
            if (area.Position.Y + area.Size.Y == roadY)
            {
                int adjustment = roadHalfWidth + 1;
                newHeight -= adjustment;
            }
        }

        // 确保尺寸不为负
        newWidth = Math.Max(10, newWidth);  // 最小宽度10
        newHeight = Math.Max(10, newHeight); // 最小高度10

        var result = new Rect2I(newX, newY, newWidth, newHeight);

        return result;
    }



    /// <summary>
    /// 道路模式数据结构
    /// </summary>
    private class RoadPattern
    {
        public List<int> HorizontalLines { get; set; } = new List<int>();
        public List<int> VerticalLines { get; set; } = new List<int>();
        public List<OptimizedRoadSystem.RoadSegment> DiagonalSegments { get; set; } = new List<OptimizedRoadSystem.RoadSegment>();
    }

    /// <summary>
    /// 水平分割（上下分割）
    /// </summary>
    private List<Rect2I> SplitHorizontally(Rect2I area)
    {
        int halfHeight = area.Size.Y / 2;
        return new List<Rect2I>
        {
            new Rect2I(area.Position.X, area.Position.Y, area.Size.X, halfHeight),                    // 上半部分
            new Rect2I(area.Position.X, area.Position.Y + halfHeight, area.Size.X, halfHeight)       // 下半部分
        };
    }

    /// <summary>
    /// 垂直分割（左右分割）
    /// </summary>
    private List<Rect2I> SplitVertically(Rect2I area)
    {
        int halfWidth = area.Size.X / 2;
        return new List<Rect2I>
        {
            new Rect2I(area.Position.X, area.Position.Y, halfWidth, area.Size.Y),                    // 左半部分
            new Rect2I(area.Position.X + halfWidth, area.Position.Y, halfWidth, area.Size.Y)         // 右半部分
        };
    }

    /// <summary>
    /// 四象限分割
    /// </summary>
    private List<Rect2I> SplitIntoQuadrants(Rect2I area)
    {
        int halfWidth = area.Size.X / 2;
        int halfHeight = area.Size.Y / 2;
        return new List<Rect2I>
        {
            new Rect2I(area.Position.X, area.Position.Y, halfWidth, halfHeight),                                    // 左上
            new Rect2I(area.Position.X + halfWidth, area.Position.Y, halfWidth, halfHeight),                       // 右上
            new Rect2I(area.Position.X, area.Position.Y + halfHeight, halfWidth, halfHeight),                      // 左下
            new Rect2I(area.Position.X + halfWidth, area.Position.Y + halfHeight, halfWidth, halfHeight)           // 右下
        };
    }



    /// <summary>
    /// 检查道路线段是否真正穿过区域（更严格的检测）
    /// </summary>
    private bool DoesSegmentIntersectArea(OptimizedRoadSystem.RoadSegment segment, Rect2I area)
    {
        // 使用道路的实际宽度，不添加额外缓冲
        int halfWidth = Math.Max(1, segment.Width / 2);

        int areaMinX = area.Position.X;
        int areaMaxX = area.Position.X + area.Size.X;
        int areaMinY = area.Position.Y;
        int areaMaxY = area.Position.Y + area.Size.Y;

        // 根据道路类型进行不同的相交检查
        if (segment.IsHorizontal)
        {
            return DoesHorizontalRoadIntersectArea(segment, area, halfWidth);
        }
        else if (segment.IsVertical)
        {
            return DoesVerticalRoadIntersectArea(segment, area, halfWidth);
        }
        else
        {
            // 斜线段：使用更精确的相交检测
            return DoesDiagonalRoadIntersectArea(segment, area, halfWidth);
        }
    }

    /// <summary>
    /// 检查水平道路是否真正穿过区域
    /// </summary>
    private bool DoesHorizontalRoadIntersectArea(OptimizedRoadSystem.RoadSegment segment, Rect2I area, int halfWidth)
    {
        int roadY = segment.StartPoint.Y;
        int segMinX = Math.Min(segment.StartPoint.X, segment.EndPoint.X);
        int segMaxX = Math.Max(segment.StartPoint.X, segment.EndPoint.X);

        int areaMinX = area.Position.X;
        int areaMaxX = area.Position.X + area.Size.X;
        int areaMinY = area.Position.Y;
        int areaMaxY = area.Position.Y + area.Size.Y;

        // 检查道路是否在区域的Y范围内
        if (roadY < areaMinY - halfWidth || roadY > areaMaxY + halfWidth)
        {
            return false; // 道路在区域的上方或下方，不相交
        }

        // 检查道路是否真正穿过区域（而不仅仅是接触）
        // 道路必须从区域的一侧延伸到另一侧，或者至少有一部分在区域内部
        bool roadStartsBeforeArea = segMinX < areaMinX;
        bool roadEndsAfterArea = segMaxX > areaMaxX;
        bool roadStartsInArea = segMinX >= areaMinX && segMinX <= areaMaxX;
        bool roadEndsInArea = segMaxX >= areaMinX && segMaxX <= areaMaxX;

        // 道路穿过区域的条件：
        // 1. 道路从区域外开始，在区域外结束，但穿过区域
        // 2. 道路的一端在区域内，另一端在区域外
        // 3. 道路完全在区域内
        return (roadStartsBeforeArea && roadEndsAfterArea) ||  // 完全穿过
               (roadStartsInArea || roadEndsInArea) ||          // 一端在区域内
               (segMinX <= areaMaxX && segMaxX >= areaMinX);    // 有重叠部分
    }

    /// <summary>
    /// 检查垂直道路是否真正穿过区域
    /// </summary>
    private bool DoesVerticalRoadIntersectArea(OptimizedRoadSystem.RoadSegment segment, Rect2I area, int halfWidth)
    {
        int roadX = segment.StartPoint.X;
        int segMinY = Math.Min(segment.StartPoint.Y, segment.EndPoint.Y);
        int segMaxY = Math.Max(segment.StartPoint.Y, segment.EndPoint.Y);

        int areaMinX = area.Position.X;
        int areaMaxX = area.Position.X + area.Size.X;
        int areaMinY = area.Position.Y;
        int areaMaxY = area.Position.Y + area.Size.Y;

        // 检查道路是否在区域的X范围内
        if (roadX < areaMinX - halfWidth || roadX > areaMaxX + halfWidth)
        {
            return false; // 道路在区域的左侧或右侧，不相交
        }

        // 检查道路是否真正穿过区域
        bool roadStartsBeforeArea = segMinY < areaMinY;
        bool roadEndsAfterArea = segMaxY > areaMaxY;
        bool roadStartsInArea = segMinY >= areaMinY && segMinY <= areaMaxY;
        bool roadEndsInArea = segMaxY >= areaMinY && segMaxY <= areaMaxY;

        return (roadStartsBeforeArea && roadEndsAfterArea) ||  // 完全穿过
               (roadStartsInArea || roadEndsInArea) ||          // 一端在区域内
               (segMinY <= areaMaxY && segMaxY >= areaMinY);    // 有重叠部分
    }

    /// <summary>
    /// 检查斜线道路是否真正穿过区域
    /// </summary>
    private bool DoesDiagonalRoadIntersectArea(OptimizedRoadSystem.RoadSegment segment, Rect2I area, int halfWidth)
    {
        // 对于斜线道路，使用更精确的线段-矩形相交检测
        // 首先检查道路端点是否在区域内
        if (IsPointInArea(segment.StartPoint, area) || IsPointInArea(segment.EndPoint, area))
        {
            return true;
        }

        // 然后检查道路是否与区域边界相交
        return LineIntersectsRectangle(segment.StartPoint, segment.EndPoint, area);
    }

    /// <summary>
    /// 检查点是否在区域内（考虑一定的容差）
    /// </summary>
    private bool IsPointInArea(Vector2I point, Rect2I area)
    {
        return point.X >= area.Position.X && point.X <= area.Position.X + area.Size.X &&
               point.Y >= area.Position.Y && point.Y <= area.Position.Y + area.Size.Y;
    }

    /// <summary>
    /// 检查线段是否与矩形相交（用于斜线段）
    /// </summary>
    private bool LineIntersectsRectangle(Vector2I lineStart, Vector2I lineEnd, Rect2I rect)
    {
        // 检查线段端点是否在矩形内
        if (rect.HasPoint(lineStart) || rect.HasPoint(lineEnd))
        {
            return true;
        }

        // 检查线段是否与矩形的四条边相交
        Vector2I[] rectCorners = {
            new Vector2I(rect.Position.X, rect.Position.Y),                           // 左上
            new Vector2I(rect.Position.X + rect.Size.X, rect.Position.Y),            // 右上
            new Vector2I(rect.Position.X + rect.Size.X, rect.Position.Y + rect.Size.Y), // 右下
            new Vector2I(rect.Position.X, rect.Position.Y + rect.Size.Y)              // 左下
        };

        // 检查与四条边的相交
        for (int i = 0; i < 4; i++)
        {
            Vector2I edge1 = rectCorners[i];
            Vector2I edge2 = rectCorners[(i + 1) % 4];

            if (LinesIntersect(lineStart, lineEnd, edge1, edge2))
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// 检查线段是否与矩形相交（考虑线段宽度，用于斜线段道路）
    /// </summary>
    private bool LineIntersectsRectangleWithWidth(Vector2I lineStart, Vector2I lineEnd, Rect2I rect, int halfWidth)
    {
        // 对于斜线段，我们扩展矩形边界来模拟道路宽度的影响
        Rect2I expandedRect = new Rect2I(
            rect.Position.X - halfWidth,
            rect.Position.Y - halfWidth,
            rect.Size.X + 2 * halfWidth,
            rect.Size.Y + 2 * halfWidth
        );

        // 使用扩展后的矩形进行标准线段相交检查
        return LineIntersectsRectangle(lineStart, lineEnd, expandedRect);
    }

    /// <summary>
    /// 检查两条线段是否相交
    /// </summary>
    private bool LinesIntersect(Vector2I line1Start, Vector2I line1End, Vector2I line2Start, Vector2I line2End)
    {
        // 使用向量叉积判断线段相交
        int d1 = CrossProduct(line2End - line2Start, line1Start - line2Start);
        int d2 = CrossProduct(line2End - line2Start, line1End - line2Start);
        int d3 = CrossProduct(line1End - line1Start, line2Start - line1Start);
        int d4 = CrossProduct(line1End - line1Start, line2End - line1Start);

        if (((d1 > 0 && d2 < 0) || (d1 < 0 && d2 > 0)) &&
            ((d3 > 0 && d4 < 0) || (d3 < 0 && d4 > 0)))
        {
            return true;
        }

        // 检查共线的情况
        if (d1 == 0 && IsPointOnSegment(line1Start, line2Start, line2End)) return true;
        if (d2 == 0 && IsPointOnSegment(line1End, line2Start, line2End)) return true;
        if (d3 == 0 && IsPointOnSegment(line2Start, line1Start, line1End)) return true;
        if (d4 == 0 && IsPointOnSegment(line2End, line1Start, line1End)) return true;

        return false;
    }

    /// <summary>
    /// 计算二维向量叉积
    /// </summary>
    private int CrossProduct(Vector2I a, Vector2I b)
    {
        return a.X * b.Y - a.Y * b.X;
    }

    /// <summary>
    /// 检查点是否在线段上
    /// </summary>
    private bool IsPointOnSegment(Vector2I point, Vector2I segStart, Vector2I segEnd)
    {
        return point.X >= Math.Min(segStart.X, segEnd.X) && point.X <= Math.Max(segStart.X, segEnd.X) &&
            point.Y >= Math.Min(segStart.Y, segEnd.Y) && point.Y <= Math.Max(segStart.Y, segEnd.Y);
    }




}