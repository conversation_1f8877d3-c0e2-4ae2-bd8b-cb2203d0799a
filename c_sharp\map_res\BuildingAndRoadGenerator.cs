using Godot;
using System;
using GameEnum;
using delaunay_algorithm;
using System.Collections.Generic;
using Godot.NativeInterop;
using System.Linq;

public partial class RoomAndSmallRoadGenerator
{
    public SimpleRoomPreloadSystem simpleRoomPreloadSystem;
    public OptimizedRoadSystem roadSystem;

    public RoomAndSmallRoadGenerator(SimpleRoomPreloadSystem simpleRoomPreloadSystem,OptimizedRoadSystem roadSystem)
    {
        this.simpleRoomPreloadSystem = simpleRoomPreloadSystem;
        this.roadSystem = roadSystem;
    }

    /// <summary>
    /// 小路段数据结构
    /// </summary>
    public class LocalRoadSegment
    {
        public Vector2I StartPoint { get; set; }
        public Vector2I EndPoint { get; set; }
        public int Width { get; set; } = 4;
        public bool IsHorizontal { get; set; }

        /// <summary>
        /// 获取小路的中心线坐标（用于建筑朝向）
        /// </summary>
        public int GetCenterCoordinate()
        {
            return IsHorizontal ? StartPoint.Y : StartPoint.X;
        }

        /// <summary>
        /// 获取小路的长度范围
        /// </summary>
        public (int start, int end) GetRange()
        {
            if (IsHorizontal)
            {
                return (Math.Min(StartPoint.X, EndPoint.X), Math.Max(StartPoint.X, EndPoint.X));
            }
            else
            {
                return (Math.Min(StartPoint.Y, EndPoint.Y), Math.Max(StartPoint.Y, EndPoint.Y));
            }
        }
    }

    /// <summary>
    /// 建筑配置
    /// </summary>
    public class BuildingConfig
    {
        public int LocalRoadWidth { get; set; } = 4;           // 小路宽度
        public int BuildingToRoadDistance { get; set; } = 6;   // 建筑到小路距离
        public int BuildingSpacing { get; set; } = 8;          // 建筑间距
        public RoomType DefaultRoomType { get; set; } = RoomType.BasicRoom; // 默认房间类型
    }


    private BuildingConfig buildingConfig = new BuildingConfig();

    /// <summary>
    /// 为建筑区域生成建筑和小路
    /// </summary>
    public void GenerateBuildingsAndLocalRoads(Rect2I buildingArea, region_mes city)
    {
        GD.Print($"处理建筑区域: {buildingArea}");

        // 获取房间尺寸
        Vector2I roomSize = simpleRoomPreloadSystem.GetDefaultSizeForRoomType(buildingConfig.DefaultRoomType);

        // 1. 找到最近的主路
        var nearestMainRoad = FindNearestMainRoad(buildingArea);

        // 2. 确定区域的主要方向（长边方向）
        bool isWiderThanTaller = buildingArea.Size.X > buildingArea.Size.Y;

        // 3. 计算能放置的建筑数量
        int buildingsPerRow, buildingsPerColumn;
        if (isWiderThanTaller)
        {
            // 区域更宽，建筑沿X轴排列，小路是水平的（分隔Y方向的建筑行）
            buildingsPerRow = CalculateBuildingsAlongAxis(buildingArea.Size.X, roomSize.X, buildingConfig.BuildingSpacing);
            buildingsPerColumn = CalculateBuildingsAlongAxis(buildingArea.Size.Y, roomSize.Y, buildingConfig.BuildingSpacing);
        }
        else
        {
            // 区域更高，建筑沿Y轴排列，小路是垂直的（分隔X方向的建筑列）
            buildingsPerRow = CalculateBuildingsAlongAxis(buildingArea.Size.X, roomSize.X, buildingConfig.BuildingSpacing);
            buildingsPerColumn = CalculateBuildingsAlongAxis(buildingArea.Size.Y, roomSize.Y, buildingConfig.BuildingSpacing);
        }

        GD.Print($"  区域尺寸: {buildingArea.Size}, 房间尺寸: {roomSize}");
        GD.Print($"  预计建筑布局: {buildingsPerRow}×{buildingsPerColumn} = {buildingsPerRow * buildingsPerColumn}个建筑");

        // 4. 生成小路和建筑
        if (isWiderThanTaller)
        {
            // 区域更宽：生成水平小路，分隔建筑行
            GenerateHorizontalRoadsWithFacingBuildings(buildingArea, roomSize, buildingsPerRow, buildingsPerColumn, nearestMainRoad);
        }
        else
        {
            // 区域更高：生成垂直小路，分隔建筑列
            GenerateVerticalRoadsWithFacingBuildings(buildingArea, roomSize, buildingsPerRow, buildingsPerColumn, nearestMainRoad);
        }
    }

    /// <summary>
    /// 找到距离建筑区域最近的主路
    /// </summary>
    private OptimizedRoadSystem.RoadSegment FindNearestMainRoad(Rect2I buildingArea)
    {
        // 直接获取主路，提高效率
        var mainRoads = roadSystem.GetMainRoadSegments();
        OptimizedRoadSystem.RoadSegment nearestRoad = null;
        float minDistance = float.MaxValue;

        Vector2I areaCenter = new Vector2I(
            buildingArea.Position.X + buildingArea.Size.X / 2,
            buildingArea.Position.Y + buildingArea.Size.Y / 2
        );

        foreach (var road in mainRoads)
        {
            // 计算区域中心到道路的距离
            float distance = CalculateDistanceToRoad(areaCenter, road);
            if (distance < minDistance)
            {
                minDistance = distance;
                nearestRoad = road;
            }
        }

        if (nearestRoad != null)
        {
            GD.Print($"  找到最近主路: 距离={minDistance:F1}, 类型={(nearestRoad.IsHorizontal ? "水平" : "垂直")}");
        }
        else
        {
            GD.Print($"  未找到主路，将从区域默认位置开始生成");
        }

        return nearestRoad;
    }

    /// <summary>
    /// 计算点到道路的距离
    /// </summary>
    private float CalculateDistanceToRoad(Vector2I point, OptimizedRoadSystem.RoadSegment road)
    {
        if (road.IsHorizontal)
        {
            // 水平道路，计算到Y坐标的距离
            int roadY = (road.StartPoint.Y + road.EndPoint.Y) / 2;
            return Math.Abs(point.Y - roadY);
        }
        else if (road.IsVertical)
        {
            // 垂直道路，计算到X坐标的距离
            int roadX = (road.StartPoint.X + road.EndPoint.X) / 2;
            return Math.Abs(point.X - roadX);
        }
        else
        {
            // 对角线道路，计算到线段的距离（简化为到起点的距离）
            int dx = point.X - road.StartPoint.X;
            int dy = point.Y - road.StartPoint.Y;
            return (float)Math.Sqrt(dx * dx + dy * dy);
        }
    }

    /// <summary>
    /// 确定水平小路布局的起始Y位置（靠近主路的一边）
    /// </summary>
    private int DetermineHorizontalStartPosition(Rect2I buildingArea, OptimizedRoadSystem.RoadSegment nearestMainRoad)
    {
        if (nearestMainRoad == null)
        {
            // 没有主路，从区域顶部开始
            return buildingArea.Position.Y;
        }

        // 计算主路的Y坐标
        int mainRoadY = (nearestMainRoad.StartPoint.Y + nearestMainRoad.EndPoint.Y) / 2;

        // 判断主路在区域的上方还是下方
        int areaCenterY = buildingArea.Position.Y + buildingArea.Size.Y / 2;

        if (mainRoadY < areaCenterY)
        {
            // 主路在区域上方，从区域顶部开始生成
            GD.Print($"    主路在区域上方 (主路Y={mainRoadY}, 区域中心Y={areaCenterY})，从顶部开始");
            return buildingArea.Position.Y;
        }
        else
        {
            // 主路在区域下方，从区域底部开始生成（需要反向计算）
            GD.Print($"    主路在区域下方 (主路Y={mainRoadY}, 区域中心Y={areaCenterY})，从底部开始");
            // 这里暂时还是从顶部开始，后续可以实现反向生成
            return buildingArea.Position.Y;
        }
    }

    /// <summary>
    /// 确定垂直小路布局的起始X位置（靠近主路的一边）
    /// </summary>
    private int DetermineVerticalStartPosition(Rect2I buildingArea, OptimizedRoadSystem.RoadSegment nearestMainRoad)
    {
        if (nearestMainRoad == null)
        {
            // 没有主路，从区域左侧开始
            return buildingArea.Position.X;
        }

        // 计算主路的X坐标
        int mainRoadX = (nearestMainRoad.StartPoint.X + nearestMainRoad.EndPoint.X) / 2;

        // 判断主路在区域的左侧还是右侧
        int areaCenterX = buildingArea.Position.X + buildingArea.Size.X / 2;

        if (mainRoadX < areaCenterX)
        {
            // 主路在区域左侧，从区域左侧开始生成
            GD.Print($"    主路在区域左侧 (主路X={mainRoadX}, 区域中心X={areaCenterX})，从左侧开始");
            return buildingArea.Position.X;
        }
        else
        {
            // 主路在区域右侧，从区域右侧开始生成（需要反向计算）
            GD.Print($"    主路在区域右侧 (主路X={mainRoadX}, 区域中心X={areaCenterX})，从右侧开始");
            // 这里暂时还是从左侧开始，后续可以实现反向生成
            return buildingArea.Position.X;
        }
    }

    /// <summary>
    /// 计算沿某个轴能放置的建筑数量
    /// </summary>
    private int CalculateBuildingsAlongAxis(int availableSpace, int buildingSize, int spacing)
    {
        if (availableSpace <= buildingSize) return 0;

        // 计算能放置的建筑数量：(总空间 - 第一个建筑) / (建筑尺寸 + 间距) + 1
        return Math.Max(0, (availableSpace - buildingSize) / (buildingSize + spacing) + 1);
    }

    /// <summary>
    /// 生成水平小路和面对面的建筑（区域更宽）
    /// </summary>
    private void GenerateHorizontalRoadsWithFacingBuildings(Rect2I buildingArea, Vector2I roomSize, int buildingsPerRow, int buildingsPerColumn, OptimizedRoadSystem.RoadSegment nearestMainRoad)
    {
        GD.Print($"  生成水平小路布局 (区域更宽) - 建筑面对面排列");

        // 计算建筑块的精确高度需求
        int roadWidth = buildingConfig.LocalRoadWidth;
        int buildingToRoadDistance = buildingConfig.BuildingToRoadDistance;
        int blockSpacing = buildingConfig.BuildingSpacing; // 建筑块之间的额外间距

        // 单个建筑块内部高度 = 上排建筑 + 间距 + 小路 + 间距 + 下排建筑
        int singleBlockHeight = roomSize.Y + buildingToRoadDistance + roadWidth + buildingToRoadDistance + roomSize.Y;

        // 建筑块总高度（包括与下一个建筑块的间距）= 内部高度 + 块间距
        int totalBlockHeight = singleBlockHeight + blockSpacing;

        // 计算能放置多少个完整的建筑块（最后一个不需要额外间距）
        int blockCount = Math.Max(1, (buildingArea.Size.Y - singleBlockHeight) / totalBlockHeight + 1);

        // 验证最后一个建筑块是否能完整放置
        int requiredHeight = (blockCount - 1) * totalBlockHeight + singleBlockHeight;
        if (requiredHeight > buildingArea.Size.Y)
        {
            blockCount--;
        }

        // 确定生成起点（靠近主路的一边）
        int startY = DetermineHorizontalStartPosition(buildingArea, nearestMainRoad);

        GD.Print($"  房间尺寸: {roomSize.X}×{roomSize.Y}");
        GD.Print($"  单个建筑块高度: {singleBlockHeight} (建筑{roomSize.Y} + 间距{buildingToRoadDistance} + 小路{roadWidth} + 间距{buildingToRoadDistance} + 建筑{roomSize.Y})");
        GD.Print($"  建筑块间距: {blockSpacing}");
        GD.Print($"  总建筑块高度: {totalBlockHeight} (包括间距)");
        GD.Print($"  区域高度: {buildingArea.Size.Y}, 可放置建筑块数量: {blockCount}");
        GD.Print($"  生成起点: Y={startY} (靠近主路)");

        if (blockCount == 0)
        {
            GD.Print($"  警告：区域高度不足以放置任何建筑块");
            return;
        }

        // 为每个建筑块生成小路和建筑
        int currentY = startY;
        for (int blockIndex = 0; blockIndex < blockCount; blockIndex++)
        {
            int blockStartY = currentY;
            int blockEndY = blockStartY + singleBlockHeight;

            GD.Print($"    生成完整建筑块 {blockIndex + 1}/{blockCount}: Y={blockStartY} 到 Y={blockEndY}");
            GenerateBuildingBlockWithHorizontalRoad(buildingArea, roomSize, blockStartY, blockEndY, buildingsPerRow);

            currentY = blockStartY + totalBlockHeight; // 移动到下一个建筑块位置
        }

        // 检查剩余空间是否可以放置单排建筑+小路
        int remainingSpace = buildingArea.Position.Y + buildingArea.Size.Y - currentY;
        int singleRowHeight = roomSize.Y + buildingToRoadDistance + roadWidth; // 一排建筑 + 间距 + 小路

        if (remainingSpace >= singleRowHeight)
        {
            GD.Print($"    剩余空间 {remainingSpace} 可放置单排建筑+小路 (需要 {singleRowHeight})");
            GenerateSingleRowWithHorizontalRoad(buildingArea, roomSize, currentY, buildingArea.Position.Y + buildingArea.Size.Y, buildingsPerRow);
        }
        else
        {
            GD.Print($"    剩余空间 {remainingSpace} 不足以放置单排建筑+小路 (需要 {singleRowHeight})");
        }
    }

    /// <summary>
    /// 生成垂直小路和面对面的建筑（区域更高）
    /// </summary>
    private void GenerateVerticalRoadsWithFacingBuildings(Rect2I buildingArea, Vector2I roomSize, int buildingsPerRow, int buildingsPerColumn, OptimizedRoadSystem.RoadSegment nearestMainRoad)
    {
        GD.Print($"  生成垂直小路布局 (区域更高) - 建筑面对面排列");

        // 计算建筑块的精确宽度需求
        int roadWidth = buildingConfig.LocalRoadWidth;
        int buildingToRoadDistance = buildingConfig.BuildingToRoadDistance;
        int blockSpacing = buildingConfig.BuildingSpacing; // 建筑块之间的额外间距

        // 单个建筑块内部宽度 = 左列建筑 + 间距 + 小路 + 间距 + 右列建筑
        int singleBlockWidth = roomSize.X + buildingToRoadDistance + roadWidth + buildingToRoadDistance + roomSize.X;

        // 建筑块总宽度（包括与下一个建筑块的间距）= 内部宽度 + 块间距
        int totalBlockWidth = singleBlockWidth + blockSpacing;

        // 计算能放置多少个完整的建筑块（最后一个不需要额外间距）
        int blockCount = Math.Max(1, (buildingArea.Size.X - singleBlockWidth) / totalBlockWidth + 1);

        // 验证最后一个建筑块是否能完整放置
        int requiredWidth = (blockCount - 1) * totalBlockWidth + singleBlockWidth;
        if (requiredWidth > buildingArea.Size.X)
        {
            blockCount--;
        }

        // 确定生成起点（靠近主路的一边）
        int startX = DetermineVerticalStartPosition(buildingArea, nearestMainRoad);

        GD.Print($"  房间尺寸: {roomSize.X}×{roomSize.Y}");
        GD.Print($"  单个建筑块宽度: {singleBlockWidth} (建筑{roomSize.X} + 间距{buildingToRoadDistance} + 小路{roadWidth} + 间距{buildingToRoadDistance} + 建筑{roomSize.X})");
        GD.Print($"  建筑块间距: {blockSpacing}");
        GD.Print($"  总建筑块宽度: {totalBlockWidth} (包括间距)");
        GD.Print($"  区域宽度: {buildingArea.Size.X}, 可放置建筑块数量: {blockCount}");
        GD.Print($"  生成起点: X={startX} (靠近主路)");

        if (blockCount == 0)
        {
            GD.Print($"  警告：区域宽度不足以放置任何建筑块");
            return;
        }

        // 为每个建筑块生成小路和建筑
        int currentX = startX;
        for (int blockIndex = 0; blockIndex < blockCount; blockIndex++)
        {
            int blockStartX = currentX;
            int blockEndX = blockStartX + singleBlockWidth;

            GD.Print($"    生成完整建筑块 {blockIndex + 1}/{blockCount}: X={blockStartX} 到 X={blockEndX}");
            GenerateBuildingBlockWithVerticalRoad(buildingArea, roomSize, blockStartX, blockEndX, buildingsPerColumn);

            currentX = blockStartX + totalBlockWidth; // 移动到下一个建筑块位置
        }

        // 检查剩余空间是否可以放置单列建筑+小路
        int remainingSpace = buildingArea.Position.X + buildingArea.Size.X - currentX;
        int singleColumnWidth = roomSize.X + buildingToRoadDistance + roadWidth; // 一列建筑 + 间距 + 小路

        if (remainingSpace >= singleColumnWidth)
        {
            GD.Print($"    剩余空间 {remainingSpace} 可放置单列建筑+小路 (需要 {singleColumnWidth})");
            GenerateSingleColumnWithVerticalRoad(buildingArea, roomSize, currentX, buildingArea.Position.X + buildingArea.Size.X, buildingsPerColumn);
        }
        else
        {
            GD.Print($"    剩余空间 {remainingSpace} 不足以放置单列建筑+小路 (需要 {singleColumnWidth})");
        }
    }

    /// <summary>
    /// 生成一个建筑块（水平小路版本）
    /// </summary>
    private void GenerateBuildingBlockWithHorizontalRoad(Rect2I buildingArea, Vector2I roomSize, int blockStartY, int blockEndY, int buildingsPerRow)
    {
        int roadWidth = buildingConfig.LocalRoadWidth;
        int buildingToRoadDistance = buildingConfig.BuildingToRoadDistance;

        // 计算建筑块的布局
        // 布局：上排建筑 + 间距 + 小路 + 间距 + 下排建筑
        int totalRequiredHeight = roomSize.Y + buildingToRoadDistance + roadWidth + buildingToRoadDistance + roomSize.Y;

        // 检查建筑块是否有足够空间
        if (blockEndY - blockStartY < totalRequiredHeight)
        {
            GD.Print($"      建筑块空间不足: 需要{totalRequiredHeight}, 可用{blockEndY - blockStartY}");
            return;
        }

        // 计算各部分的Y坐标
        int upperBuildingY = blockStartY;  // 上排建筑Y坐标
        int roadStartY = upperBuildingY + roomSize.Y + buildingToRoadDistance;  // 小路起始Y坐标
        int roadCenterY = roadStartY + roadWidth / 2;  // 小路中心Y坐标
        int lowerBuildingY = roadStartY + roadWidth + buildingToRoadDistance;  // 下排建筑Y坐标

        GD.Print($"      建筑块布局: 上排Y={upperBuildingY}, 小路Y={roadCenterY}, 下排Y={lowerBuildingY}");
        GD.Print($"      建筑尺寸: {roomSize.X}×{roomSize.Y}, 总高度需求: {totalRequiredHeight}");

        // 生成水平小路
        var road = new LocalRoadSegment
        {
            StartPoint = new Vector2I(buildingArea.Position.X, roadCenterY),
            EndPoint = new Vector2I(buildingArea.Position.X + buildingArea.Size.X, roadCenterY),
            Width = roadWidth,
            IsHorizontal = true
        };
        AddLocalRoadToSystem(road);

        // 沿X轴放置建筑
        int buildingSpacing = roomSize.X + buildingConfig.BuildingSpacing;
        int buildingCount = 0;

        for (int x = buildingArea.Position.X; x <= buildingArea.Position.X + buildingArea.Size.X - roomSize.X; x += buildingSpacing)
        {
            // 上排建筑（朝下）
            Vector2I upperPos = new Vector2I(x, upperBuildingY);
            if (IsValidBuildingPosition(upperPos, roomSize, buildingArea))
            {
                simpleRoomPreloadSystem.RegisterRoomWithRandomName(upperPos, buildingConfig.DefaultRoomType);
                GD.Print($"      上排建筑: {upperPos} -> {upperPos + roomSize} (朝下，面向小路 Y={roadCenterY})");
                buildingCount++;
            }

            // 下排建筑（朝上）
            Vector2I lowerPos = new Vector2I(x, lowerBuildingY);
            if (IsValidBuildingPosition(lowerPos, roomSize, buildingArea))
            {
                simpleRoomPreloadSystem.RegisterRoomWithRandomName(lowerPos, buildingConfig.DefaultRoomType);
                GD.Print($"      下排建筑: {lowerPos} -> {lowerPos + roomSize} (朝上，面向小路 Y={roadCenterY})");
                buildingCount++;
            }
        }

        GD.Print($"      建筑块完成，共放置 {buildingCount} 个建筑");
    }

    /// <summary>
    /// 生成一个建筑块（垂直小路版本）
    /// </summary>
    private void GenerateBuildingBlockWithVerticalRoad(Rect2I buildingArea, Vector2I roomSize, int blockStartX, int blockEndX, int buildingsPerColumn)
    {
        int roadWidth = buildingConfig.LocalRoadWidth;
        int buildingToRoadDistance = buildingConfig.BuildingToRoadDistance;

        // 计算建筑块的布局
        // 布局：左列建筑 + 间距 + 小路 + 间距 + 右列建筑
        int totalRequiredWidth = roomSize.X + buildingToRoadDistance + roadWidth + buildingToRoadDistance + roomSize.X;

        // 检查建筑块是否有足够空间
        if (blockEndX - blockStartX < totalRequiredWidth)
        {
            GD.Print($"      建筑块空间不足: 需要{totalRequiredWidth}, 可用{blockEndX - blockStartX}");
            return;
        }

        // 计算各部分的X坐标
        int leftBuildingX = blockStartX;  // 左列建筑X坐标
        int roadStartX = leftBuildingX + roomSize.X + buildingToRoadDistance;  // 小路起始X坐标
        int roadCenterX = roadStartX + roadWidth / 2;  // 小路中心X坐标
        int rightBuildingX = roadStartX + roadWidth + buildingToRoadDistance;  // 右列建筑X坐标

        GD.Print($"      建筑块布局: 左列X={leftBuildingX}, 小路X={roadCenterX}, 右列X={rightBuildingX}");
        GD.Print($"      建筑尺寸: {roomSize.X}×{roomSize.Y}, 总宽度需求: {totalRequiredWidth}");

        // 生成垂直小路
        var road = new LocalRoadSegment
        {
            StartPoint = new Vector2I(roadCenterX, buildingArea.Position.Y),
            EndPoint = new Vector2I(roadCenterX, buildingArea.Position.Y + buildingArea.Size.Y),
            Width = roadWidth,
            IsHorizontal = false
        };
        AddLocalRoadToSystem(road);

        // 沿Y轴放置建筑
        int buildingSpacing = roomSize.Y + buildingConfig.BuildingSpacing;
        int buildingCount = 0;

        for (int y = buildingArea.Position.Y; y <= buildingArea.Position.Y + buildingArea.Size.Y - roomSize.Y; y += buildingSpacing)
        {
            // 左列建筑（朝右）
            Vector2I leftPos = new Vector2I(leftBuildingX, y);
            if (IsValidBuildingPosition(leftPos, roomSize, buildingArea))
            {
                simpleRoomPreloadSystem.RegisterRoomWithRandomName(leftPos, buildingConfig.DefaultRoomType);
                GD.Print($"      左列建筑: {leftPos} -> {leftPos + roomSize} (朝右，面向小路 X={roadCenterX})");
                buildingCount++;
            }

            // 右列建筑（朝左）
            Vector2I rightPos = new Vector2I(rightBuildingX, y);
            if (IsValidBuildingPosition(rightPos, roomSize, buildingArea))
            {
                simpleRoomPreloadSystem.RegisterRoomWithRandomName(rightPos, buildingConfig.DefaultRoomType);
                GD.Print($"      右列建筑: {rightPos} -> {rightPos + roomSize} (朝左，面向小路 X={roadCenterX})");
                buildingCount++;
            }
        }

        GD.Print($"      建筑块完成，共放置 {buildingCount} 个建筑");
    }

    /// <summary>
    /// 生成单排建筑和水平小路（用于填充剩余空间）
    /// </summary>
    private void GenerateSingleRowWithHorizontalRoad(Rect2I buildingArea, Vector2I roomSize, int startY, int endY, int buildingsPerRow)
    {
        int roadWidth = buildingConfig.LocalRoadWidth;
        int buildingToRoadDistance = buildingConfig.BuildingToRoadDistance;

        // 计算布局：建筑 + 间距 + 小路
        int buildingY = startY;
        int roadY = buildingY + roomSize.Y + buildingToRoadDistance + roadWidth / 2;

        GD.Print($"      生成单排建筑: 建筑Y={buildingY}, 小路Y={roadY}");

        // 生成水平小路
        var road = new LocalRoadSegment
        {
            StartPoint = new Vector2I(buildingArea.Position.X, roadY),
            EndPoint = new Vector2I(buildingArea.Position.X + buildingArea.Size.X, roadY),
            Width = roadWidth,
            IsHorizontal = true
        };
        AddLocalRoadToSystem(road);

        // 沿X轴放置建筑
        int buildingSpacing = roomSize.X + buildingConfig.BuildingSpacing;
        int buildingCount = 0;

        for (int x = buildingArea.Position.X; x <= buildingArea.Position.X + buildingArea.Size.X - roomSize.X; x += buildingSpacing)
        {
            Vector2I buildingPos = new Vector2I(x, buildingY);
            if (IsValidBuildingPosition(buildingPos, roomSize, buildingArea))
            {
                simpleRoomPreloadSystem.RegisterRoomWithRandomName(buildingPos, buildingConfig.DefaultRoomType);
                GD.Print($"      单排建筑: {buildingPos} -> {buildingPos + roomSize} (朝下，面向小路 Y={roadY})");
                buildingCount++;
            }
        }

        GD.Print($"      单排完成，共放置 {buildingCount} 个建筑");
    }

    /// <summary>
    /// 生成单列建筑和垂直小路（用于填充剩余空间）
    /// </summary>
    private void GenerateSingleColumnWithVerticalRoad(Rect2I buildingArea, Vector2I roomSize, int startX, int endX, int buildingsPerColumn)
    {
        int roadWidth = buildingConfig.LocalRoadWidth;
        int buildingToRoadDistance = buildingConfig.BuildingToRoadDistance;

        // 计算布局：建筑 + 间距 + 小路
        int buildingX = startX;
        int roadX = buildingX + roomSize.X + buildingToRoadDistance + roadWidth / 2;

        GD.Print($"      生成单列建筑: 建筑X={buildingX}, 小路X={roadX}");

        // 生成垂直小路
        var road = new LocalRoadSegment
        {
            StartPoint = new Vector2I(roadX, buildingArea.Position.Y),
            EndPoint = new Vector2I(roadX, buildingArea.Position.Y + buildingArea.Size.Y),
            Width = roadWidth,
            IsHorizontal = false
        };
        AddLocalRoadToSystem(road);

        // 沿Y轴放置建筑
        int buildingSpacing = roomSize.Y + buildingConfig.BuildingSpacing;
        int buildingCount = 0;

        for (int y = buildingArea.Position.Y; y <= buildingArea.Position.Y + buildingArea.Size.Y - roomSize.Y; y += buildingSpacing)
        {
            Vector2I buildingPos = new Vector2I(buildingX, y);
            if (IsValidBuildingPosition(buildingPos, roomSize, buildingArea))
            {
                simpleRoomPreloadSystem.RegisterRoomWithRandomName(buildingPos, buildingConfig.DefaultRoomType);
                GD.Print($"      单列建筑: {buildingPos} -> {buildingPos + roomSize} (朝右，面向小路 X={roadX})");
                buildingCount++;
            }
        }

        GD.Print($"      单列完成，共放置 {buildingCount} 个建筑");
    }

    /// <summary>
    /// 将小路添加到道路系统
    /// </summary>
    private void AddLocalRoadToSystem(LocalRoadSegment localRoad)
    {
        // 创建RoadSegment对象
        var roadSegment = new OptimizedRoadSystem.RoadSegment
        {
            StartPoint = localRoad.StartPoint,
            EndPoint = localRoad.EndPoint,
            Width = localRoad.Width,
            Type = OptimizedRoadSystem.RoadType.LocalRoad
        };

        // 添加到道路系统
        roadSystem.AddRoadSegment(roadSegment);
        GD.Print($"    小路已添加到系统: {localRoad.StartPoint} -> {localRoad.EndPoint} (宽度: {localRoad.Width})");
    }

    /// <summary>
    /// 检查建筑位置是否有效（基本检查）
    /// </summary>
    private bool IsValidBuildingPosition(Vector2I position, Vector2I size, Rect2I buildingArea)
    {
        // 检查是否在建筑区域内
        if (position.X < buildingArea.Position.X ||
            position.Y < buildingArea.Position.Y ||
            position.X + size.X > buildingArea.Position.X + buildingArea.Size.X ||
            position.Y + size.Y > buildingArea.Position.Y + buildingArea.Size.Y)
        {
            return false;
        }

        // 检查是否与主路冲突
        Rect2I buildingRect = new Rect2I(position, size);
        var mainRoads = roadSystem.GetMainRoadSegments();

        foreach (var road in mainRoads)
        {
            // 简单的边界框检查
            if (!(road.MaxX < buildingRect.Position.X ||
                  road.MinX > buildingRect.Position.X + buildingRect.Size.X ||
                  road.MaxY < buildingRect.Position.Y ||
                  road.MinY > buildingRect.Position.Y + buildingRect.Size.Y))
            {
                return false; // 与主路冲突
            }
        }

        return true;
    }

    /// <summary>
    /// 计算小路位置分布
    /// </summary>
    private List<int> CalculateRoadPositions(int startPos, int totalSize, int roadCount, bool isVertical)
    {
        var positions = new List<int>();

        if (roadCount <= 0) return positions;

        if (roadCount == 1)
        {
            // 只有一条小路，放在中间
            positions.Add(startPos + totalSize / 2);
        }
        else
        {
            // 多条小路，均匀分布
            int spacing = totalSize / (roadCount + 1);
            for (int i = 1; i <= roadCount; i++)
            {
                positions.Add(startPos + spacing * i);
            }
        }

        return positions;
    }

    /// <summary>
    /// 填满整个区域放置建筑
    /// </summary>
    private void FillAreaWithBuildings(Rect2I buildingArea, Vector2I roomSize, List<LocalRoadSegment> localRoads, bool roadsAreHorizontal)
    {
        int buildingCount = 0;

        // 计算建筑放置的网格
        int spacingX = roomSize.X + buildingConfig.BuildingSpacing;
        int spacingY = roomSize.Y + buildingConfig.BuildingSpacing;

        // 遍历整个区域，尝试在每个可能的位置放置建筑
        for (int x = buildingArea.Position.X; x <= buildingArea.Position.X + buildingArea.Size.X - roomSize.X; x += spacingX)
        {
            for (int y = buildingArea.Position.Y; y <= buildingArea.Position.Y + buildingArea.Size.Y - roomSize.Y; y += spacingY)
            {
                Vector2I buildingPos = new Vector2I(x, y);

                // 检查这个位置是否有效（不与小路冲突）
                if (IsValidBuildingPositionWithLocalRoads(buildingPos, roomSize, buildingArea, localRoads))
                {
                    simpleRoomPreloadSystem.RegisterRoomWithRandomName(buildingPos, buildingConfig.DefaultRoomType);
                    buildingCount++;

                    // 找到最近的小路用于调试信息
                    var nearestRoad = FindNearestLocalRoad(buildingPos, localRoads);
                    if (nearestRoad != null)
                    {
                        string roadInfo = nearestRoad.IsHorizontal ? $"Y={nearestRoad.StartPoint.Y}" : $"X={nearestRoad.StartPoint.X}";
                        GD.Print($"    放置建筑: {buildingPos} (朝向小路 {roadInfo})");
                    }
                }
            }
        }

        GD.Print($"  总共放置了 {buildingCount} 个建筑");
    }

    /// <summary>
    /// 检查建筑位置是否有效（考虑小路）
    /// </summary>
    private bool IsValidBuildingPositionWithLocalRoads(Vector2I position, Vector2I size, Rect2I buildingArea, List<LocalRoadSegment> localRoads)
    {
        // 检查是否在建筑区域内
        if (position.X < buildingArea.Position.X ||
            position.Y < buildingArea.Position.Y ||
            position.X + size.X > buildingArea.Position.X + buildingArea.Size.X ||
            position.Y + size.Y > buildingArea.Position.Y + buildingArea.Size.Y)
        {
            return false;
        }

        // 检查是否与小路冲突
        Rect2I buildingRect = new Rect2I(position, size);

        foreach (var road in localRoads)
        {
            if (DoesRectIntersectRoad(buildingRect, road))
            {
                return false;
            }
        }

        // 检查是否与主路冲突
        var mainRoads = roadSystem.GetMainRoadSegments();
        foreach (var road in mainRoads)
        {
            if (!(road.MaxX < buildingRect.Position.X ||
                  road.MinX > buildingRect.Position.X + buildingRect.Size.X ||
                  road.MaxY < buildingRect.Position.Y ||
                  road.MinY > buildingRect.Position.Y + buildingRect.Size.Y))
            {
                return false;
            }
        }

        return true;
    }



    /// <summary>
    /// 找到最近的小路
    /// </summary>
    private LocalRoadSegment FindNearestLocalRoad(Vector2I position, List<LocalRoadSegment> localRoads)
    {
        if (localRoads.Count == 0) return null;

        LocalRoadSegment nearest = localRoads[0];
        float minDistance = CalculateDistanceToLocalRoad(position, nearest);

        foreach (var road in localRoads.Skip(1))
        {
            float distance = CalculateDistanceToLocalRoad(position, road);
            if (distance < minDistance)
            {
                minDistance = distance;
                nearest = road;
            }
        }

        return nearest;
    }

    /// <summary>
    /// 计算点到小路的距离
    /// </summary>
    private float CalculateDistanceToLocalRoad(Vector2I position, LocalRoadSegment road)
    {
        if (road.IsHorizontal)
        {
            // 水平小路，计算到Y坐标的距离
            return Math.Abs(position.Y - road.StartPoint.Y);
        }
        else
        {
            // 垂直小路，计算到X坐标的距离
            return Math.Abs(position.X - road.StartPoint.X);
        }
    }

    /// <summary>
    /// 检查矩形是否与小路相交
    /// </summary>
    private bool DoesRectIntersectRoad(Rect2I rect, LocalRoadSegment road)
    {
        int roadHalfWidth = road.Width / 2;

        if (road.IsHorizontal)
        {
            // 水平小路
            int roadY = road.StartPoint.Y;
            int roadStartX = Math.Min(road.StartPoint.X, road.EndPoint.X);
            int roadEndX = Math.Max(road.StartPoint.X, road.EndPoint.X);

            // 检查Y轴重叠
            if (rect.Position.Y + rect.Size.Y <= roadY - roadHalfWidth ||
                rect.Position.Y >= roadY + roadHalfWidth)
            {
                return false;
            }

            // 检查X轴重叠
            return !(rect.Position.X + rect.Size.X <= roadStartX ||
                     rect.Position.X >= roadEndX);
        }
        else
        {
            // 垂直小路
            int roadX = road.StartPoint.X;
            int roadStartY = Math.Min(road.StartPoint.Y, road.EndPoint.Y);
            int roadEndY = Math.Max(road.StartPoint.Y, road.EndPoint.Y);

            // 检查X轴重叠
            if (rect.Position.X + rect.Size.X <= roadX - roadHalfWidth ||
                rect.Position.X >= roadX + roadHalfWidth)
            {
                return false;
            }

            // 检查Y轴重叠
            return !(rect.Position.Y + rect.Size.Y <= roadStartY ||
                     rect.Position.Y >= roadEndY);
        }
    }
}