using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// 随机名称生成器
/// </summary>
public static class RandomNameGenerator
{
    private static RandomNumberGenerator rng = new RandomNumberGenerator();
    
    // 中文姓氏
    private static readonly string[] ChineseSurnames = {
        "李", "王", "张", "刘", "陈", "杨", "赵", "黄", "周", "吴",
        "徐", "孙", "胡", "朱", "高", "林", "何", "郭", "马", "罗",
        "梁", "宋", "郑", "谢", "韩", "唐", "冯", "于", "董", "萧"
    };
    
    // 中文名字
    private static readonly string[] ChineseGivenNames = {
        "伟", "芳", "娜", "秀英", "敏", "静", "丽", "强", "磊", "军",
        "洋", "勇", "艳", "杰", "娟", "涛", "明", "超", "秀兰", "霞",
        "平", "刚", "桂英", "建华", "文", "华", "金凤", "素英", "建国", "德华"
    };
    
    // 地名前缀
    private static readonly string[] PlacePrefixes = {
        "新", "老", "大", "小", "东", "西", "南", "北", "中", "上",
        "下", "前", "后", "左", "右", "高", "低", "长", "短", "金",
        "银", "红", "绿", "蓝", "白", "黑", "青", "紫", "明", "暗"
    };
    
    // 地名后缀
    private static readonly string[] PlaceSuffixes = {
        "村", "镇", "城", "市", "区", "县", "府", "州", "港", "湾",
        "山", "河", "湖", "海", "岛", "桥", "路", "街", "巷", "院",
        "园", "庄", "坊", "堡", "关", "寨", "营", "站", "厂", "店"
    };
    
    // 房间类型名称
    private static readonly string[] RoomTypes = {
        "卧室", "客厅", "厨房", "书房", "储藏室", "工作室", "会议室", "休息室",
        "办公室", "实验室", "图书馆", "餐厅", "浴室", "阳台", "地下室", "阁楼",
        "车库", "花房", "音乐室", "游戏室", "健身房", "影音室", "茶室", "酒窖"
    };
    
    // 建筑风格
    private static readonly string[] BuildingStyles = {
        "古典", "现代", "传统", "简约", "豪华", "朴素", "精致", "宏伟",
        "温馨", "典雅", "时尚", "复古", "工业", "田园", "欧式", "中式",
        "日式", "美式", "地中海", "北欧", "东南亚", "巴洛克", "哥特", "维多利亚"
    };
    
    // 颜色
    private static readonly string[] Colors = {
        "红", "橙", "黄", "绿", "青", "蓝", "紫", "粉", "白", "黑",
        "灰", "棕", "金", "银", "铜", "翠", "碧", "赤", "朱", "墨"
    };
    
    // 材质
    private static readonly string[] Materials = {
        "木", "石", "铁", "钢", "玻璃", "陶", "瓷", "竹", "藤", "布",
        "皮", "丝", "棉", "麻", "羊毛", "大理石", "花岗岩", "水晶", "琥珀", "玉"
    };
    
    /// <summary>
    /// 设置随机种子
    /// </summary>
    public static void SetSeed(ulong seed)
    {
        rng.Seed = seed;
    }
    
    /// <summary>
    /// 生成中文人名
    /// </summary>
    public static string GenerateChineseName()
    {
        var surname = ChineseSurnames[rng.RandiRange(0, ChineseSurnames.Length - 1)];
        var givenName = ChineseGivenNames[rng.RandiRange(0, ChineseGivenNames.Length - 1)];
        return surname + givenName;
    }
    
    /// <summary>
    /// 生成地名
    /// </summary>
    public static string GeneratePlaceName()
    {
        var prefix = PlacePrefixes[rng.RandiRange(0, PlacePrefixes.Length - 1)];
        var suffix = PlaceSuffixes[rng.RandiRange(0, PlaceSuffixes.Length - 1)];
        return prefix + suffix;
    }
    
    /// <summary>
    /// 生成房间名称
    /// </summary>
    public static string GenerateRoomName()
    {
        var roomType = RoomTypes[rng.RandiRange(0, RoomTypes.Length - 1)];
        
        // 30% 概率添加修饰词
        if (rng.RandiRange(0, 99) < 30)
        {
            var modifier = GetRandomModifier();
            return modifier + roomType;
        }
        
        return roomType;
    }
    
    /// <summary>
    /// 生成建筑名称
    /// </summary>
    public static string GenerateBuildingName()
    {
        var style = BuildingStyles[rng.RandiRange(0, BuildingStyles.Length - 1)];
        var suffix = new[] { "大厦", "公寓", "别墅", "住宅", "楼", "院", "馆", "中心" };
        var buildingSuffix = suffix[rng.RandiRange(0, suffix.Length - 1)];
        
        return style + buildingSuffix;
    }
    
    /// <summary>
    /// 生成商店名称
    /// </summary>
    public static string GenerateShopName()
    {
        var shopTypes = new[] { "商店", "超市", "便利店", "专卖店", "精品店", "小铺", "商行", "贸易行" };
        var shopType = shopTypes[rng.RandiRange(0, shopTypes.Length - 1)];
        
        // 50% 概率添加人名或地名
        if (rng.RandiRange(0, 99) < 50)
        {
            var name = rng.RandiRange(0, 1) == 0 ? GenerateChineseName() : GeneratePlaceName();
            return name + shopType;
        }
        
        var modifier = GetRandomModifier();
        return modifier + shopType;
    }
    
    /// <summary>
    /// 生成区域名称（用于地图区域）
    /// </summary>
    public static string GenerateRegionName()
    {
        var regionTypes = new[] { "区域", "地带", "片区", "街区", "社区", "园区", "开发区", "新区" };
        var regionType = regionTypes[rng.RandiRange(0, regionTypes.Length - 1)];
        
        var prefix = PlacePrefixes[rng.RandiRange(0, PlacePrefixes.Length - 1)];
        return prefix + regionType;
    }
    
    /// <summary>
    /// 生成带编号的名称
    /// </summary>
    public static string GenerateNumberedName(string baseName, int number)
    {
        var numberFormats = new[] { "#{0}", "第{0}号", "{0}号", "No.{0}", "{0}区" };
        var format = numberFormats[rng.RandiRange(0, numberFormats.Length - 1)];
        
        return baseName + string.Format(format, number);
    }
    
    /// <summary>
    /// 生成随机修饰词
    /// </summary>
    private static string GetRandomModifier()
    {
        var modifierTypes = rng.RandiRange(0, 2);
        
        return modifierTypes switch
        {
            0 => Colors[rng.RandiRange(0, Colors.Length - 1)],
            1 => Materials[rng.RandiRange(0, Materials.Length - 1)],
            _ => BuildingStyles[rng.RandiRange(0, BuildingStyles.Length - 1)]
        };
    }
    
    /// <summary>
    /// 生成复合名称（组合多个元素）
    /// </summary>
    public static string GenerateCompoundName(params string[] elements)
    {
        if (elements.Length == 0) return "未命名";
        
        var shuffled = elements.OrderBy(x => rng.Randi()).ToArray();
        var count = Math.Min(rng.RandiRange(1, 3), shuffled.Length);
        
        return string.Join("", shuffled.Take(count));
    }
    
    /// <summary>
    /// 根据类型生成名称
    /// </summary>
    public static string GenerateNameByType(string type)
    {
        return type.ToLower() switch
        {
            "person" or "人名" => GenerateChineseName(),
            "place" or "地名" => GeneratePlaceName(),
            "room" or "房间" => GenerateRoomName(),
            "building" or "建筑" => GenerateBuildingName(),
            "shop" or "商店" => GenerateShopName(),
            "region" or "区域" => GenerateRegionName(),
            _ => GeneratePlaceName()
        };
    }

    /// <summary>
    /// 生成游戏角色名称
    /// </summary>
    public static string GenerateCharacterName()
    {
        // 70% 概率生成中文名，30% 概率生成带称号的名字
        if (rng.RandiRange(0, 99) < 70)
        {
            return GenerateChineseName();
        }
        else
        {
            var titles = new[] { "勇士", "法师", "盗贼", "战士", "弓手", "牧师", "骑士", "刺客" };
            var title = titles[rng.RandiRange(0, titles.Length - 1)];
            var name = GenerateChineseName();
            return $"{title}{name}";
        }
    }

    /// <summary>
    /// 生成NPC名称
    /// </summary>
    public static string GenerateNPCName()
    {
        var professions = new[] { "商人", "铁匠", "药师", "守卫", "农夫", "渔夫", "猎人", "学者", "工匠", "厨师" };
        var profession = professions[rng.RandiRange(0, professions.Length - 1)];
        var name = GenerateChineseName();

        return rng.RandiRange(0, 1) == 0 ? $"{profession}{name}" : $"{name}({profession})";
    }

    /// <summary>
    /// 生成道具名称
    /// </summary>
    public static string GenerateItemName(string itemType = "")
    {
        var qualities = new[] { "普通", "精良", "稀有", "史诗", "传说" };
        var quality = qualities[rng.RandiRange(0, qualities.Length - 1)];

        var itemNames = itemType.ToLower() switch
        {
            "weapon" or "武器" => new[] { "剑", "刀", "枪", "弓", "法杖", "匕首", "锤", "斧" },
            "armor" or "护甲" => new[] { "头盔", "胸甲", "护腿", "靴子", "手套", "盾牌", "斗篷", "腰带" },
            "potion" or "药水" => new[] { "生命药水", "魔法药水", "力量药水", "敏捷药水", "智力药水", "解毒剂", "恢复药剂", "强化药剂" },
            _ => new[] { "宝石", "卷轴", "符文", "水晶", "护符", "戒指", "项链", "手镯" }
        };

        var itemName = itemNames[rng.RandiRange(0, itemNames.Length - 1)];
        var modifier = GetRandomModifier();

        return $"{quality}的{modifier}{itemName}";
    }

    /// <summary>
    /// 生成任务名称
    /// </summary>
    public static string GenerateQuestName()
    {
        var questTypes = new[] { "寻找", "击败", "收集", "护送", "调查", "拯救", "探索", "清理" };
        var questType = questTypes[rng.RandiRange(0, questTypes.Length - 1)];

        var targets = new[] { "失落的宝藏", "神秘的遗迹", "危险的怪物", "重要的信息", "珍贵的材料", "被困的村民", "古老的秘密", "邪恶的势力" };
        var target = targets[rng.RandiRange(0, targets.Length - 1)];

        return $"{questType}{target}";
    }
}
