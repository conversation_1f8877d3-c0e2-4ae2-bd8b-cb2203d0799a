{
    // 使用 IntelliSense 了解相关属性。 
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Play in Editor",
            "type": "godot-mono",
            "mode": "playInEditor",
            "request": "launch"
        },
        {
            "name": "Launch",
            "type": "godot-mono",
            "request": "launch",
            "mode": "executable",
            "preLaunchTask": "build",
            "executable": "D:/godot/Godot_v4.4.1-stable_mono_win64/Godot_v4.4.1-stable_mono_win64.exe",
            // See which arguments are available here:
            // https://docs.godotengine.org/en/stable/getting_started/editor/command_line_tutorial.html
            "executableArguments": [
                "--path",
                "${workspaceRoot}"
            ]
        },
        {
            "name": "Launch (Select Scene)",
            "type": "godot-mono",
            "request": "launch",
            "mode": "executable",
            "preLaunchTask": "build",
            "executable": "D:/godot/Godot_v4.4.1-stable_mono_win64/Godot_v4.4.1-stable_mono_win64.exe",
            // See which arguments are available here:
            // https://docs.godotengine.org/en/stable/getting_started/editor/command_line_tutorial.html
            "executableArguments": [
                "--path",
                "${workspaceRoot}",
                "${command:SelectLaunchScene}"
            ]
        },
        {
            "name": "Attach",
            "type": "godot-mono",
            "request": "attach",
            "address": "localhost",
            "port": 23685
        }
    ]
}