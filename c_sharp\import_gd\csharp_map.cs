using Godot;
using System;
using System.Collections.Generic;
using System.Linq;
using GameEnum;

public partial class csharp_map : Node2D
{

	//tilemap,显示地形
	private TileMapLayer char_show;

	private Node2D bg_node;
	private static readonly Texture2D GRASS = GD.Load<Texture2D>("res://assetes/mine/tilemap/grass.png");
	private static readonly Texture2D SAND = GD.Load<Texture2D>("res://assetes/mine/tilemap/sand.png");
	private static readonly Texture2D SOIL = GD.Load<Texture2D>("res://assetes/mine/tilemap/soil.png");
	private static readonly Texture2D WATER = GD.Load<Texture2D>("res://assetes/mine/tilemap/water.png");
	private static readonly Texture2D SEA = GD.Load<Texture2D>("res://assetes/mine/tilemap/sea.png");
	private static readonly Texture2D STREET = GD.Load<Texture2D>("res://assetes/mine/tilemap/street.png");
	private static readonly Texture2D ROAD = GD.Load<Texture2D>("res://assetes/mine/tilemap/road.png");
	private static readonly Texture2D FLOOR_1 = GD.Load<Texture2D>("res://assetes/mine/tilemap/floor_1.png");
	private readonly Dictionary<Char_type, Dictionary<string, object>> char_tile_d = new()
	{
		{ Char_type.Street, new Dictionary<string, object> {
			{ "id", 0 },
			{ "atlas_coords", new Vector2I(0, 0) },
			{ "tilemap", STREET },
			{ "color", Colors.DarkGray }
		}},
		{ Char_type.Road, new Dictionary<string, object> {
			{ "id", 0 },
			{ "atlas_coords", new Vector2I(1, 0) },
			{ "tilemap", ROAD },
			{ "color", Colors.DimGray }
		}},
		{ Char_type.Soil, new Dictionary<string, object> {
			{ "id", 0 },
			{ "atlas_coords", new Vector2I(2, 0) },
			{ "tilemap", SOIL },
			{ "color", new Color("d2691e") } // CHOCOLATE
		}},
		{ Char_type.Grass, new Dictionary<string, object> {
			{ "id", 0 },
			{ "atlas_coords", new Vector2I(3, 0) },
			{ "tilemap", GRASS },
			{ "color", Colors.ForestGreen }
		}},
		{ Char_type.Forest, new Dictionary<string, object> {
			{ "id", 0 },
			{ "atlas_coords", new Vector2I(4, 0) },
			{ "color", Colors.DarkGray }
		}},
		{ Char_type.Water, new Dictionary<string, object> {
			{ "id", 0 },
			{ "atlas_coords", new Vector2I(5, 0) },
			{ "tilemap", WATER },
			{ "color", Colors.ForestGreen }
		}},
		{ Char_type.SEA, new Dictionary<string, object> {
			{ "id", 0 },
			{ "atlas_coords", new Vector2I(5, 1) },
			{ "tilemap", SEA },
			{ "color", Colors.DodgerBlue }
		}},
		{ Char_type.FLOOR_1, new Dictionary<string, object> {
			{ "id", 0 },
			{ "atlas_coords", new Vector2I(0, 0) },
			{ "tilemap", FLOOR_1 },
			{ "color", Colors.LightGray }
		}},
		{ Char_type.Sand, new Dictionary<string, object> {
			{ "id", 0 },
			{ "atlas_coords", new Vector2I(0, 0) },
			{ "tilemap", SAND },
			{ "color", new Color("d2691e")  }
		}},
	};


	// 用于创建地图,底层地形由算法决定
    private static readonly Dictionary<string, Vector2I> colorg_dir = new()
	{
		// 原有的过渡瓦片
		{ "2,2,1,2", new Vector2I(0,0) },
		{ "2,1,2,1", new Vector2I(1,0) },
		{ "1,2,1,1", new Vector2I(2,0) },
		{ "2,2,1,1", new Vector2I(3,0) },

		{ "1,2,2,1", new Vector2I(0,1) },
		{ "2,1,1,1", new Vector2I(1,1) },
		{ "1,1,1,1", new Vector2I(2,1) },  // 纯地形1
		{ "1,1,1,2", new Vector2I(3,1) },

		{ "2,1,2,2", new Vector2I(0,2) },
		{ "1,1,2,2", new Vector2I(1,2) },
		{ "1,1,2,1", new Vector2I(2,2) },
		{ "1,2,1,2", new Vector2I(3,2) },

		{ "2,2,2,2", new Vector2I(0,3) },  // 纯地形2
		{ "2,2,2,1", new Vector2I(1,3) },
		{ "2,1,1,2", new Vector2I(2,3) },
		{ "1,2,2,2", new Vector2I(3,3) },
	};
	// 信号定义
	[Signal] public delegate void MapManSignalEventHandler();

	// 地图噪声
	[Export] public FastNoiseLite Noise { get; set; }

	[Export] Map_res map_res = new Map_res(); // 地图资源

	// 地图显示UI
	private MapDisplayUI mapDisplayUI;

	// 用于储存背景贴图的tilemap的node2d节点
	private Dictionary<(Char_type, Char_type), TileMapLayer> bg_kind_dir = new Dictionary<(Char_type, Char_type), TileMapLayer>();

	// 在类中添加,删除专用
	private Dictionary<Vector2I, (Char_type, Char_type)> tilemap_record = new Dictionary<Vector2I, (Char_type, Char_type)>();

	// 瓦片地图管理器
	private Dictionary<Vector2I, object> describe_map = new();

	// 背景贴图的瓦片场景
	private PackedScene TILE_MAP_BG = GD.Load<PackedScene>("res://tool/tilemap/tile_map_bg.tscn");

	private Node GameGlobal; // 声明为类字段
	private Node GameEnum; // 声明为类字段

	private Vector2I block_size = new(); // 区块大小
	private int block_show_quantity = new(); // 区块显示数量
	private int edge_left = new();
	private int edge_right = new();
	private int edge_top = new();
	private int edge_bottom = new();


	public override void _Ready()
	{
		GameGlobal = GetNode("/root/GameGlobal");
		GameEnum = GetNode("/root/GameEnum");

		block_size = (Vector2I)GameGlobal.Get("block_size");
		block_show_quantity = (int)GameGlobal.Get("block_show_quantity");
		edge_left = (int)GameGlobal.Get("edge_left");
		edge_right = (int)GameGlobal.Get("edge_right");
		edge_top = (int)GameGlobal.Get("edge_top");
		edge_bottom = (int)GameGlobal.Get("edge_bottom");

		//设置子节点
		char_show = GetNode<TileMapLayer>("char_show");
		bg_node = GetNode<Node2D>("bg_node");
		mapDisplayUI = GetNode<MapDisplayUI>("../Camera2D/map");

		//生成大地图
		map_res.map_res_init(Noise,edge_left, edge_right, edge_top, edge_bottom, block_size, block_show_quantity);

		// RandomNumberGenerator rng = new RandomNumberGenerator();
		// rng.Randomize();
		// Noise.Seed = (int)rng.Randi();
		// BlockSet(Vector2I.Zero);
		UpdateBlock();

		// 初始化地图显示UI
		InitializeMapDisplayUI();
	}

	/// <summary>
	/// 初始化地图显示UI
	/// </summary>
	private void InitializeMapDisplayUI()
	{
		// mapDisplayUI = new MapDisplayUI();
		// AddChild(mapDisplayUI);

		// 设置地图数据
		mapDisplayUI.SetMapData(map_res);

		GD.Print("地图显示UI已初始化，按M键切换显示，按F5键刷新");
	}



	// 区块变化时更新
	public void UpdateBlock()
	{
		Vector2I current_block = (Vector2I)GameGlobal.Get("current_block");
		GetShowArea(current_block);
		BlockSet(current_block);
	}

	/// <summary>
	/// 获取当前显示的区域（基于中心区块和显示数量）
	/// </summary>
	/// <returns>显示区域的矩形（像素坐标）</returns>
	private void GetShowArea(Vector2I center_block)
	{
		// 计算显示的区块范围
		var blockStartX = center_block.X - block_show_quantity;
		var blockEndX = center_block.X + block_show_quantity;
		var blockStartY = center_block.Y - block_show_quantity;
		var blockEndY = center_block.Y + block_show_quantity;
		
		// 转换坐标
		var pixelStartX = blockStartX * block_size.X;
		var pixelStartY = blockStartY * block_size.Y;
		var pixelEndX = (blockEndX+1)*block_size.X;
		var pixelEndY = (blockEndY+1)*block_size.Y;

		map_res.updata_area_roompreload(new Rect2I(
			pixelStartX,
			pixelStartY,
			pixelEndX - pixelStartX,
			pixelEndY - pixelStartY
		));
	}

	// 显示的区块记录
	private HashSet<Vector2I> last_show_blocks = new();

	// 记录实际已加载的区块（包括地形数据和瓦片）
	private HashSet<Vector2I> loaded_blocks = new();
	// 获取显示的区块坐标
	public HashSet<Vector2I> GetShowBlocks(Vector2I center_block)
	{
		var result = new HashSet<Vector2I>();
		for (int x = center_block.X - block_show_quantity; x <= center_block.X + block_show_quantity; x++)
		{
			for (int y = center_block.Y - block_show_quantity; y <= center_block.Y + block_show_quantity; y++)
			{
				result.Add(new Vector2I(x, y));
			}
		}
		return result;
	}

	// 获取保持加载的区块坐标（卸载半径更大）
	public HashSet<Vector2I> GetKeepLoadedBlocks(Vector2I center_block, int extraRadius = 1)
	{
		var result = new HashSet<Vector2I>();
		int keepRadius = block_show_quantity + extraRadius;
		for (int x = center_block.X - keepRadius; x <= center_block.X + keepRadius; x++)
		{
			for (int y = center_block.Y - keepRadius; y <= center_block.Y + keepRadius; y++)
			{
				result.Add(new Vector2I(x, y));
			}
		}
		return result;
	}
	//是否显示底层字符
	const bool is_show_char = false;
	const bool show_time = false;
	public async void BlockSet(Vector2I block_coord)
	{
		ulong time_cnt = Time.GetTicksMsec();
		ulong time_save = Time.GetTicksMsec();
		const ulong time_wite = 10000; // 每帧最大耗时(ms)

		//获取区块
		var current_show_blocks = GetShowBlocks(block_coord);

		// 获取保持加载的区块（卸载半径更大，避免边界抖动）
		// 临时减小extraRadius来测试卸载功能
		var keep_loaded_blocks = GetKeepLoadedBlocks(block_coord, 2);

		// 新增区块
		var add_blocks = new HashSet<Vector2I>(current_show_blocks);
		add_blocks.ExceptWith(last_show_blocks);

		// 待卸载区块：从实际已加载的区块中，移除需要保持的区块
		var remove_blocks = new HashSet<Vector2I>(loaded_blocks);
		remove_blocks.ExceptWith(keep_loaded_blocks);

		// 处理新增区块
		foreach (var block in add_blocks)
		{
			BlockCreateInDir(block);
			if (is_show_char)
				BlockAdd(block);
			loaded_blocks.Add(block); // 记录已加载的区块
			// if (Time.GetTicksMsec() - time_save > time_wite)
			// {
			// 	await ToSignal(GetTree(), "process_frame");
			// 	time_save = Time.GetTicksMsec();
			// }
		}
		if (show_time)
			GD.Print("底层加载耗时间:", Time.GetTicksMsec() - time_cnt);
		time_cnt = Time.GetTicksMsec();

		foreach (var block in add_blocks)
		{
			BgBlockAdd(block);
			// if (Time.GetTicksMsec() - time_save > time_wite)
			// {
			// 	await ToSignal(GetTree(), "process_frame");
			// 	time_save = Time.GetTicksMsec();
			// }
		}
		if (show_time)
			GD.Print("图块加载时间:", Time.GetTicksMsec() - time_cnt);
		time_cnt = Time.GetTicksMsec();

		// 处理待卸载区块
		// GD.Print($"需要卸载的区块数量: {remove_blocks.Count}");
		foreach (var block in remove_blocks)
		{
			// GD.Print($"卸载区块: {block}");
			BlockClearInDir(block);
			BgBlockClear(block);
			if (is_show_char)
				BlockClear(block);
			loaded_blocks.Remove(block); // 从已加载记录中移除
			// if (Time.GetTicksMsec() - time_save > time_wite)
			// {
			// 	await ToSignal(GetTree(), "process_frame");
			// 	time_save = Time.GetTicksMsec();
			// }
		}
		if (show_time)
			GD.Print("卸载时间:", Time.GetTicksMsec() - time_cnt);
		// GD.Print("区块个数",describe_map.Count);
		// 更新记录
		last_show_blocks = current_show_blocks;

	}

	// 显示底层区块
	public void BlockAdd(Vector2I coordinates)
	{
		for (int x = coordinates.X * block_size.X; x <= (coordinates.X + 1) * block_size.X; x++)
		{
			for (int y = coordinates.Y * block_size.Y; y <= (coordinates.Y + 1) * block_size.Y; y++)
			{
				Char_type type = (Char_type)describe_map[new Vector2I(x, y)];
				char_show.SetCell(new Vector2I(x, y), (int)char_tile_d[type]["id"], (Vector2I)char_tile_d[type]["atlas_coords"]);
			}
		}
	}

	public void BlockClear(Vector2I coordinates)
	{
		for (int x = coordinates.X * block_size.X; x <= (coordinates.X + 1) * block_size.X; x++)
		{
			for (int y = coordinates.Y * block_size.Y; y <= (coordinates.Y + 1) * block_size.Y; y++)
			{
				char_show.EraseCell(new Vector2I(x, y));
			}
		}
	}
	//字典记录抽象
	public void BlockCreateInDir(Vector2I coordinates)
	{
		for (int x = coordinates.X * block_size.X-1; x <= (coordinates.X + 1) * block_size.X; x++)
		{
			for (int y = coordinates.Y * block_size.Y-1; y <= (coordinates.Y + 1) * block_size.Y; y++)
			{
				SetCharDir(new Vector2I(x, y));
			}
		}
	}

	public void BlockClearInDir(Vector2I coordinates)
	{
		for (int x = coordinates.X * block_size.X-1; x <= (coordinates.X + 1) * block_size.X; x++)
		{
			for (int y = coordinates.Y * block_size.Y-1; y <= (coordinates.Y + 1) * block_size.Y; y++)
			{
				describe_map.Remove(new Vector2I(x, y));
			}
		}
	}
	
	public void CreateBgTileMapLayer(Char_type t1, Char_type t2)
	{
		// 检查是否已存在，避免重复创建
		if (bg_kind_dir.ContainsKey((t1, t2)) || bg_kind_dir.ContainsKey((t2, t1)))
			return;

		// 获取贴图和颜色
		Texture2D image_r = null;
		Texture2D image_b = null;
		Color color_g = Colors.Black;

		if (char_tile_d[t1].TryGetValue("tilemap", out object tilemap_r))
			image_r = tilemap_r as Texture2D;
		if (char_tile_d[t2].TryGetValue("tilemap", out object tilemap_b))
			image_b = tilemap_b as Texture2D;

		if (char_tile_d[t1].TryGetValue("color", out object color1) && char_tile_d[t2].TryGetValue("color", out object color2))
			color_g = ((Color)color1).Lerp((Color)color2, 0.5f);

		if (image_r == null || image_b == null)
		{
			GD.Print("无法创建背景贴图层: 贴图或颜色未找到");
			return;
		}
		
		// 实例化TileMapLayer
		TileMapLayer new_tile = TILE_MAP_BG.Instantiate() as TileMapLayer;
		
		if (new_tile == null)
		{
			GD.Print("无法实例化TileMapLayer");
			return;
		}

		// 设置属性
		new_tile.Set("image_r", image_r);
		new_tile.Set("image_b", image_b);
		new_tile.Set("color_g", color_g);

		// 确保位置为零
		new_tile.Position = Vector2I.Zero;


		bg_node.AddChild(new_tile);

		// 记录到字典
		bg_kind_dir[(t1, t2)] = new_tile;

		// ✅ 添加调试信息
		GD.Print($"第 {bg_kind_dir.Count} 个 TileMapLayer:");
		GD.Print($"创建 TileMapLayer: {t1}-{t2}");
		GD.Print($"  bg_node 位置: {bg_node.Position}");
		GD.Print($"  new_tile 位置: {new_tile.Position}");
		GD.Print($"  new_tile 全局位置: {new_tile.GlobalPosition}");


	}

	/// <summary>
	/// 优化的区块背景添加：批量处理，减少重复计算
	/// </summary>
	public void BgBlockAdd(Vector2I coordinates)
	{
		// 预计算区块边界
		int startX = coordinates.X * block_size.X;
		int endX = (coordinates.X + 1) * block_size.X;
		int startY = coordinates.Y * block_size.Y;
		int endY = (coordinates.Y + 1) * block_size.Y;

		// 批量收集需要处理的坐标
		var coordsToProcess = new List<Vector2I>((endX - startX) * (endY - startY));

		for (int x = startX; x < endX; x++)
		{
			for (int y = startY; y < endY; y++)
			{
				var coord = new Vector2I(x, y);
				if (!tilemap_record.ContainsKey(coord))
				{
					coordsToProcess.Add(coord);
				}
			}
		}

		// 批量处理坐标
		BgSetTileMapBatch(coordsToProcess);
	}

	/// <summary>
	/// 批量处理背景瓦片设置，优化性能
	/// 优化策略：
	/// 1. 按地形组合分组，减少TileMapLayer切换
	/// 2. 空间局部性优化，相邻坐标一起处理
	/// 3. 预分配容器大小，减少内存分配
	/// </summary>
	private void BgSetTileMapBatch(List<Vector2I> coordinates)
	{
		if (coordinates.Count == 0) return;

		// 空间局部性优化：按坐标排序，提高缓存命中率
		coordinates.Sort((a, b) => {
			int cmp = a.Y.CompareTo(b.Y);
			return cmp != 0 ? cmp : a.X.CompareTo(b.X);
		});

		// 按地形组合分组，预分配容量
		var groupedByTerrain = new Dictionary<(Char_type, Char_type), List<(Vector2I coord, Vector2I atlas)>>(8);

		// 第一遍：收集所有需要的地形数据和atlas坐标
		foreach (var coord in coordinates)
		{
			if (!TryGetFourCornerTerrains(coord, out Char_type[] char_ary))
				continue;

			var save_ary = CountTerrainTypes(char_ary);
			var (t1, t2) = DetermineTerrainPair(save_ary);

			// 确保TileMapLayer存在
			CreateBgTileMapLayer(t1, t2);

			// 查找匹配的地形组合和atlas坐标
			if (TryGetAtlasCoords(char_ary, save_ary, out var terrainPair, out var atlasCoords))
			{
				if (!groupedByTerrain.ContainsKey(terrainPair))
					groupedByTerrain[terrainPair] = [];

				groupedByTerrain[terrainPair].Add((coord, atlasCoords));
			}
		}

		// 第二遍：按地形组合批量设置瓦片
		foreach (var group in groupedByTerrain)
		{
			var (t1, t2) = group.Key;
			var coordsAndAtlas = group.Value;

			if (bg_kind_dir.TryGetValue((t1, t2), out var tileMap))
			{
				// 批量设置同一TileMapLayer的所有瓦片
				foreach (var (coord, atlas) in coordsAndAtlas)
				{
					tileMap.SetCell(coord, 0, atlas);
					tilemap_record[coord] = group.Key;
				}
			}
		}
	}

	/// <summary>
	/// 确定地形对，提取自原BgSetTileMapByDir的逻辑
	/// </summary>
	private (Char_type t1, Char_type t2) DetermineTerrainPair(Dictionary<Char_type, int> save_ary)
	{
		var t1 = Char_type.Null;
		var t2 = Char_type.Null;

		if (save_ary.Count == 1)
		{
			var types = new List<Char_type>(save_ary.Keys);
			t1 = types[0];

			bool foundExisting = false;
			foreach (var existingKey in bg_kind_dir.Keys)
			{
				if (existingKey.Item1 == t1 || existingKey.Item2 == t1)
				{
					t1 = existingKey.Item1;
					t2 = existingKey.Item2;
					foundExisting = true;
					break;
				}
			}
			if (!foundExisting)
			{
				var all_types = new List<Char_type>((Char_type[])Enum.GetValues(typeof(Char_type)));
				all_types.Remove(Char_type.Null);
				int idx = all_types.IndexOf(t1);
				int t2_idx = (idx + 1) % all_types.Count;
				t2 = all_types[t2_idx];
			}
		}
		else if (save_ary.Count == 2)
		{
			var types = new List<Char_type>(save_ary.Keys);
			t1 = types[0];
			t2 = types[1];
		}
		else if (save_ary.Count > 2)
		{
			var sortedTerrains = save_ary.OrderByDescending(kv => kv.Value).ToList();
			t1 = sortedTerrains[0].Key;

			var candidatesForSecond = new List<Char_type>();
			for (int i = 1; i < sortedTerrains.Count; i++)
			{
				if (sortedTerrains[i].Value == sortedTerrains[1].Value)
				{
					candidatesForSecond.Add(sortedTerrains[i].Key);
				}
			}

			if (candidatesForSecond.Count > 1)
			{
				var rng = new RandomNumberGenerator();
				rng.Randomize();
				int randomIndex = rng.RandiRange(0, candidatesForSecond.Count - 1);
				t2 = candidatesForSecond[randomIndex];
			}
			else
			{
				t2 = sortedTerrains[1].Key;
			}
		}

		return (t1, t2);
	}

	/// <summary>
	/// 尝试获取atlas坐标，提取自原BgSetTileMapByDir的逻辑
	/// </summary>
	private bool TryGetAtlasCoords(Char_type[] char_ary, Dictionary<Char_type, int> save_ary,
		out (Char_type, Char_type) terrainPair, out Vector2I atlasCoords)
	{
		terrainPair = default;
		atlasCoords = default;

		foreach (var kv in bg_kind_dir)
		{
			var (type1, type2) = kv.Key;
			if (save_ary.Count >= 2 && char_ary.Contains(type1) && char_ary.Contains(type2))
			{}
			else if (save_ary.Count == 1 && (char_ary.Contains(type1) || char_ary.Contains(type2)))
			{}
			else
				continue;

			var mapped = GenerateTerrainMapping(char_ary, type1, type2, save_ary);
			string key = string.Join(",", mapped);

			if (colorg_dir.TryGetValue(key, out atlasCoords))
			{
				terrainPair = (type1, type2);
				return true;
			}
		}

		return false;
	}

	public void BgBlockClear(Vector2I coordinates)
	{
		int clearedCount = 0;
		int startX = coordinates.X * block_size.X;
		int endX = (coordinates.X + 1) * block_size.X;
		int startY = coordinates.Y * block_size.Y;
		int endY = (coordinates.Y + 1) * block_size.Y;

		for (int x = startX; x < endX; x++)
		{
			for (int y = startY; y < endY; y++)
			{
				var coord = new Vector2I(x, y);
				if (tilemap_record.ContainsKey(coord))
				{
					BgClearTileMap(coord);
					clearedCount++;
				}
			}
		}

	}

	// 根据坐标设置背景贴图
	public void BgSetTileMapByDir(Vector2I coords)
	{
		if (tilemap_record.ContainsKey(coords))
			return;
		// 优化：使用专门的方法一次性获取四个角的地形数据
		if (!TryGetFourCornerTerrains(coords, out Char_type[] char_ary))
		{
			return; // 缺少数据，直接跳过
		}

		// 优化：使用高效的地形统计方法
		var save_ary = CountTerrainTypes(char_ary);

		// 统计多地形情况（用于调试）
		if (save_ary.Count > 2)
		{
			var terrainTypes = string.Join(", ", save_ary.Select(kv => $"{kv.Key}({kv.Value})"));
			GD.Print($"发现多地形区域 {coords}: {terrainTypes}");
		}

		var t1 = Char_type.Null;
		var t2 = Char_type.Null;
		if (save_ary.Count == 1)
		{
			// 获取两种类型
			var types = new List<Char_type>(save_ary.Keys);
			t1 = types[0];


			bool foundExisting = false;
			foreach (var existingKey in bg_kind_dir.Keys)
			{
				if (existingKey.Item1 == t1 || existingKey.Item2 == t1)
				{
					// 找到现有的组合，直接使用
					t1 = existingKey.Item1;
					t2 = existingKey.Item2;
					foundExisting = true;
					break;
				}
			}
			if (!foundExisting)
			{
				// 获取所有有效枚举
				var all_types = new List<Char_type>((Char_type[])Enum.GetValues(typeof(Char_type)));
				all_types.Remove(Char_type.Null);

				// 找到t1在枚举中的索引
				int idx = all_types.IndexOf(t1);
				int t2_idx = (idx + 1) % all_types.Count; // 下一个，如果超出就回到第一个
				t2 = all_types[t2_idx];
			}
		}
		else if (save_ary.Count == 2)
		{
			// 获取两种类型
			var types = new List<Char_type>(save_ary.Keys);
			t1 = types[0];
			t2 = types[1];
		}
		else if (save_ary.Count > 2)
		{
			// 多种地形类型：首先检查是否有预设的优先组合
			var preferredPair = CheckPreferredTerrainPair(save_ary);
			if (preferredPair.HasValue)
			{
				t1 = preferredPair.Value.Item1;
				t2 = preferredPair.Value.Item2;
				// GD.Print($"多地形区域 {coords}: 使用预设组合 {t1} 和 {t2}");
			}
			else
			{
				// 没有预设组合，使用原有逻辑：选择数量最多的两种
				var sortedTerrains = save_ary.OrderByDescending(kv => kv.Value).ToList();

				// 选择第一个（数量最多的）
				t1 = sortedTerrains[0].Key;

				// 选择第二个：如果有多个数量相同的，随机选择
				var maxCount = sortedTerrains[0].Value;
				var candidatesForSecond = new List<Char_type>();

				for (int i = 1; i < sortedTerrains.Count; i++)
				{
					if (sortedTerrains[i].Value == sortedTerrains[1].Value)
					{
						candidatesForSecond.Add(sortedTerrains[i].Key);
					}
				}

				if (candidatesForSecond.Count > 1)
				{
					// 有多个数量相同的候选，随机选择
					var rng = new RandomNumberGenerator();
					rng.Randomize();
					int randomIndex = rng.RandiRange(0, candidatesForSecond.Count - 1);
					t2 = candidatesForSecond[randomIndex];
				}
				else
				{
					// 直接选择第二个
					t2 = sortedTerrains[1].Key;
				}

				// GD.Print($"多地形区域 {coords}: 选择 {t1}({save_ary[t1]}) 和 {t2}({save_ary[t2]})");
			}
		}
		CreateBgTileMapLayer(t1, t2);


		foreach (var kv in bg_kind_dir)
		{
			var (type1, type2) = kv.Key;
			if (save_ary.Count >= 2 && char_ary.Contains(type1) && char_ary.Contains(type2))
			{}
			else if (save_ary.Count == 1 && (char_ary.Contains(type1) || char_ary.Contains(type2)))
			{}
			else
				continue;
			// 生成映射，支持多种地形类型
			var mapped = GenerateTerrainMapping(char_ary, type1, type2, save_ary);

			// 形成key字符串
			string key = string.Join(",", mapped);
			if (colorg_dir.TryGetValue(key, out Vector2I atlas_coords))
			{
				var tile_map = kv.Value;
				// 设置背景贴图
				tile_map.SetCell(coords, 0, atlas_coords);
				tilemap_record[coords] = kv.Key; // 记录

				return;
			}
			else
			{
				GD.Print($"未找到对应的atlas_coords: {key}, 地形组合: {type1}-{type2}");
			}
		}
	}

	/// <summary>
	/// 尝试获取四个角的地形数据
	/// 优化：一次性检查和获取，避免重复字典查找
	/// </summary>
	[System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
	private bool TryGetFourCornerTerrains(Vector2I coords, out Char_type[] terrains)
	{
		terrains = new Char_type[4];

		// 预计算偏移坐标
		var coord1 = coords;
		var coord2 = coords + new Vector2I(1, 0);
		var coord3 = coords + new Vector2I(0, 1);
		var coord4 = coords + new Vector2I(1, 1);

		// 一次性检查并获取所有地形数据
		if (describe_map.TryGetValue(coord1, out object obj1) &&
		    describe_map.TryGetValue(coord2, out object obj2) &&
		    describe_map.TryGetValue(coord3, out object obj3) &&
		    describe_map.TryGetValue(coord4, out object obj4))
		{
			terrains[0] = (Char_type)obj1;
			terrains[1] = (Char_type)obj2;
			terrains[2] = (Char_type)obj3;
			terrains[3] = (Char_type)obj4;
			return true;
		}

		return false;
	}

	/// <summary>
	/// 高效统计地形类型出现次数
	/// 针对4个地形的特殊优化
	/// </summary>
	[System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
	private static Dictionary<Char_type, int> CountTerrainTypes(Char_type[] terrains)
	{
		var counts = new Dictionary<Char_type, int>(4); // 预分配容量

		// 针对4个地形的优化统计
		for (int i = 0; i < terrains.Length; i++)
		{
			var terrain = terrains[i];
			counts[terrain] = counts.GetValueOrDefault(terrain, 0) + 1;
		}

		return counts;
	}

	/// <summary>
	/// 生成地形映射，支持多种地形类型的智能处理
	/// 优化：使用数组参数和固定大小数组，提高性能
	/// </summary>
	private static int[] GenerateTerrainMapping(Char_type[] char_ary, Char_type type1, Char_type type2, Dictionary<Char_type, int> save_ary)
	{
		var mapped = new int[4]; // 固定大小数组，避免动态分配

		for (int i = 0; i < char_ary.Length; i++)
		{
			var terrain = char_ary[i];
			if (terrain == type1)
			{
				mapped[i] = 1;
			}
			else if (terrain == type2)
			{
				mapped[i] = 2;
			}
			else
			{
				// 处理第三种或更多地形类型
				// 策略：映射到出现次数更多的地形类型
				mapped[i] = DecideTerrainMapping(type1, type2, save_ary);
			}
		}

		return mapped;
	}

	/// <summary>
	/// 预设的优先地形组合配置
	/// 如果多地形区域中包含这些组合，优先选择这两个地形类型
	/// </summary>
	private static readonly List<(Char_type, Char_type)> PreferredTerrainPairs = new()
	{
		// 您可以在这里添加想要优先选择的地形组合
		(Char_type.Road, Char_type.Street),      // 道路+草地组合

	};

	/// <summary>
	/// 检查多地形区域中是否包含预设的优先地形组合
	/// </summary>
	/// <param name="terrainCounts">地形类型及其数量的字典</param>
	/// <returns>如果找到预设组合则返回该组合，否则返回null</returns>
	private static (Char_type, Char_type)? CheckPreferredTerrainPair(Dictionary<Char_type, int> terrainCounts)
	{
		// 遍历所有预设的优先组合
		foreach (var preferredPair in PreferredTerrainPairs)
		{
			// 检查当前地形区域是否同时包含这两种地形类型
			if (terrainCounts.ContainsKey(preferredPair.Item1) &&
			    terrainCounts.ContainsKey(preferredPair.Item2))
			{
				// 找到匹配的预设组合，直接返回
				return preferredPair;
			}
		}

		// 没有找到任何预设组合
		return null;
	}

	/// <summary>
	/// 决定额外地形类型应该映射到哪个主要地形（基于频率）
	/// </summary>
	private static int DecideTerrainMapping(Char_type type1, Char_type type2, Dictionary<Char_type, int> save_ary)
	{
		// 简化策略：映射到出现次数更多的地形类型
		int type1Count = save_ary.GetValueOrDefault(type1, 0);
		int type2Count = save_ary.GetValueOrDefault(type2, 0);

		return type1Count >= type2Count ? 1 : 2;
	}

	public void BgClearTileMap(Vector2I coords)
	{
		if (tilemap_record.TryGetValue(coords, out (Char_type, Char_type) terrainPair))
		{
			// 通过地形组合找到对应的TileMapLayer并清除瓦片
			if (bg_kind_dir.TryGetValue(terrainPair, out TileMapLayer tileMapLayer))
			{
				tileMapLayer.EraseCell(coords);
				// GD.Print($"清除瓦片 {coords} 从 {terrainPair.Item1}-{terrainPair.Item2} 层");
			}
			else
			{
				GD.Print($"警告：找不到地形组合 {terrainPair.Item1}-{terrainPair.Item2} 对应的TileMapLayer");
			}
			tilemap_record.Remove(coords);
		}
		else
		{
			// GD.Print($"坐标 {coords} 不在 tilemap_record 中");
		}
	}

	//这里进行地形标识
	public void SetCharDir(Vector2I coords)
	{
		if (describe_map.ContainsKey(coords))
			return;

		// 检查是否是全局道路
		if (map_res.IsGlobalRoad(coords))
		{
			describe_map[coords] = Char_type.Road;
			return;
		}

		// 检查是否是小路
		if (map_res.IsLocalRoad(coords))
		{
			describe_map[coords] = Char_type.Street;
			return;
		}
		
		if (map_res.is_room(coords))
		{
			// GD.Print(coords);
			describe_map[coords] = Char_type.FLOOR_1;
			return;
		}

		int wid = 8;
		// 检查是否在地图四周8格宽的海洋区域内
		// 如果坐标距离任何边界小于等于8格，就是海洋
		if (coords.X <= edge_left + wid || coords.X >= edge_right - wid ||
			coords.Y <= edge_top + wid || coords.Y >= edge_bottom - wid)
		{
			describe_map[coords] = Char_type.SEA;
			return;
		}

		// float n = Noise.GetNoise2D(coords.X, coords.Y);
		// if (n > 0.8)
		// {
		// 	describe_map[coords] = Char_type.Road;
		// 	return;
		// }
		// else if (n < -0.5)
		// {
		// 	describe_map[coords] = Char_type.Grass;
		// 	return;
		// }
		// else if (n > 0 && n < 0.5)
		// {
		// 	describe_map[coords] = Char_type.FLOOR_1;
		// 	return;
		// }

		// 默认地形
		describe_map[coords] = Char_type.Sand;
	}

}