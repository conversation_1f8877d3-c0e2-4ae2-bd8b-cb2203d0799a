using Godot;
using System;
using System.Collections.Generic;
using System.Linq;
using GameEnum;

/// <summary>
/// 房间加载状态
/// </summary>
public enum SimpleRoomLoadState
{
    Unloaded,   // 未加载
    Loaded      // 已加载
}

/// <summary>
/// 简单房间记录信息
/// </summary>
public class SimpleRoomRecord
{
    public string RoomId { get; set; }
    public Vector2I Position { get; set; }
    public Vector2I Size { get; set; }
    public ORtation Orientation { get; set; }
    public RoomType RoomType { get; set; }  // 改为枚举类型
    public SimpleRoomLoadState LoadState { get; set; } = SimpleRoomLoadState.Unloaded;
    public RoomBasic LoadedRoom { get; set; } = null;
    public double LastAccessTime { get; set; } = 0;

    /// <summary>
    /// 房间占据的区域
    /// </summary>
    public Rect2I Area
    {
        get
        {
            Vector2I actualSize = Orientation switch
            {
                ORtation.Down => Size,
                ORtation.Up => Size,
                ORtation.Left => new Vector2I(Size.Y, Size.X),
                ORtation.Right => new Vector2I(Size.Y, Size.X),
                _ => Size
            };
            
            return new Rect2I(Position, actualSize);
        }
    }

    public SimpleRoomRecord(string roomId, Vector2I position, Vector2I size, RoomType roomType, ORtation orientation = ORtation.Down)
    {
        RoomId = roomId;
        Position = position;
        Size = size;
        RoomType = roomType;
        Orientation = orientation;
        LastAccessTime = Time.GetUnixTimeFromSystem();
    }
}

/// <summary>
/// 简单房间预加载系统（无异步复杂性）
/// </summary>
public class SimpleRoomPreloadSystem
{
    private Dictionary<string, SimpleRoomRecord> roomRecords = new();
    private Dictionary<Vector2I, List<string>> spatialIndex = new();
    private Rect2I currentQueryArea = new Rect2I();
    private readonly int indexBlockSize;
    private readonly int maxLoadedRooms;

    public Func<SimpleRoomRecord, RoomBasic> RoomFactory { get; set; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="gridSize">空间索引网格大小，默认64</param>
    /// <param name="maxLoaded">最大同时加载房间数，默认20</param>
    public SimpleRoomPreloadSystem(int blockSize = 64, int maxLoaded = 40)
    {
        indexBlockSize = blockSize;
        maxLoadedRooms = maxLoaded;
        RoomFactory = CreateDefaultRoom;
    }

    /// <summary>
    /// 注册房间记录
    /// </summary>
    public void RegisterRoom(string roomId, Vector2I position, Vector2I size, RoomType roomType, ORtation orientation = ORtation.Down)
    {
        var record = new SimpleRoomRecord(roomId, position, size, roomType, orientation);

        roomRecords[roomId] = record;
        AddToSpatialIndex(record);

        // GD.Print($"注册房间: {roomId} 位置: {position} 尺寸: {size} 类型: {roomType} 朝向: {orientation}");
    }

    /// <summary>
    /// 注册房间记录（自动生成随机名称，尺寸从房间类型推断）
    /// </summary>
    public void RegisterRoomWithRandomName(Vector2I position, RoomType roomType, ORtation orientation = ORtation.Down)
    {
        Vector2I size = GetDefaultSizeForRoomType(roomType);
        string randomName = RandomNameGenerator.GenerateRoomName();
        string roomId = $"{randomName}_{position.X}_{position.Y}";

        RegisterRoom(roomId, position, size, roomType, orientation);
    }

    /// <summary>
    /// 注册房间记录（自动生成随机名称，兼容旧版本API）
    /// </summary>
    [System.Obsolete("请使用不需要size参数的重载方法")]
    public void RegisterRoomWithRandomName(Vector2I position, Vector2I size, RoomType roomType, ORtation orientation = ORtation.Down)
    {
        string randomName = RandomNameGenerator.GenerateRoomName();
        string roomId = $"{randomName}_{position.X}_{position.Y}";

        RegisterRoom(roomId, position, size, roomType, orientation);
    }

    /// <summary>
    /// 批量注册房间（自动生成随机名称，尺寸从房间类型推断）
    /// </summary>
    public void RegisterRoomsWithRandomNames(List<(Vector2I position, RoomType type, ORtation orientation)> rooms)
    {
        foreach (var (position, type, orientation) in rooms)
        {
            RegisterRoomWithRandomName(position, type, orientation);
        }

        GD.Print($"批量注册了 {rooms.Count} 个随机命名房间");
    }

    /// <summary>
    /// 批量注册房间（自动生成随机名称，兼容旧版本API）
    /// </summary>
    [System.Obsolete("请使用不需要size参数的重载方法")]
    public void RegisterRoomsWithRandomNames(List<(Vector2I position, Vector2I size, RoomType type, ORtation orientation)> rooms)
    {
        foreach (var (position, size, type, orientation) in rooms)
        {
            RegisterRoomWithRandomName(position, size, type, orientation);
        }

        GD.Print($"批量注册了 {rooms.Count} 个随机命名房间");
    }

    /// <summary>
    /// 批量注册房间
    /// </summary>
    public void RegisterRooms(List<(string id, Vector2I position, Vector2I size, RoomType type, ORtation orientation)> rooms)
    {
        foreach (var (id, position, size, type, orientation) in rooms)
        {
            RegisterRoom(id, position, size, type, orientation);
        }

        GD.Print($"批量注册了 {rooms.Count} 个房间");
    }

    /// <summary>
    /// 更新查询区域并处理房间加载/卸载
    /// </summary>
    public void UpdateQueryArea(Rect2I queryArea)
    {
        currentQueryArea = queryArea;

        var intersectingRooms = FindIntersectingRooms(queryArea);

        ProcessRoomLoading(intersectingRooms);
        ProcessRoomUnloading(queryArea);

        // GD.Print("建筑数量:",GetLoadedRoomCount());
    }

    /// <summary>
    /// 查找与指定区域相交的房间
    /// </summary>
    private List<SimpleRoomRecord> FindIntersectingRooms(Rect2I area)
    {
        var intersectingRooms = new List<SimpleRoomRecord>();
        var checkedRooms = new HashSet<string>();
        
        var gridStart = new Vector2I(area.Position.X / indexBlockSize, area.Position.Y / indexBlockSize);
        var gridEnd = new Vector2I((area.End.X - 1) / indexBlockSize, (area.End.Y - 1) / indexBlockSize);
        
        for (int x = gridStart.X; x <= gridEnd.X; x++)
        {
            for (int y = gridStart.Y; y <= gridEnd.Y; y++)
            {
                var gridPos = new Vector2I(x, y);
                if (spatialIndex.ContainsKey(gridPos))
                {
                    foreach (var roomId in spatialIndex[gridPos])
                    {
                        if (checkedRooms.Contains(roomId)) continue;
                        checkedRooms.Add(roomId);
                        
                        var record = roomRecords[roomId];
                        if (record.Area.Intersects(area))
                        {
                            intersectingRooms.Add(record);
                        }
                    }
                }
            }
        }
        
        return intersectingRooms;
    }

    /// <summary>
    /// 处理房间加载
    /// </summary>
    private void ProcessRoomLoading(List<SimpleRoomRecord> intersectingRooms)
    {
        var roomsToLoad = intersectingRooms
            .Where(r => r.LoadState == SimpleRoomLoadState.Unloaded)
            .ToList();
        
        foreach (var record in roomsToLoad)
        {
            if (GetLoadedRoomCount() >= maxLoadedRooms) break;
            
            LoadRoom(record);
        }
    }

    /// <summary>
    /// 处理房间卸载（使用扩大的卸载范围避免卡顿）
    /// </summary>
    private void ProcessRoomUnloading(Rect2I queryArea)
    {
        // 创建扩大的保持区域（比查询区域大一些，避免边界抖动）
        int unloadBuffer = Math.Max(indexBlockSize * 2, 200); // 缓冲区大小
        var expandedKeepArea = new Rect2I(
            queryArea.Position.X - unloadBuffer,
            queryArea.Position.Y - unloadBuffer,
            queryArea.Size.X + unloadBuffer * 2,
            queryArea.Size.Y + unloadBuffer * 2
        );

        // 找到扩大区域内的房间
        var roomsInExpandedArea = FindIntersectingRooms(expandedKeepArea);
        var keepRoomIds = new HashSet<string>(roomsInExpandedArea.Select(r => r.RoomId));

        // 只卸载不在扩大区域内的房间
        var roomsToUnload = roomRecords.Values
            .Where(r => r.LoadState == SimpleRoomLoadState.Loaded && !keepRoomIds.Contains(r.RoomId))
            .OrderBy(r => r.LastAccessTime)
            .ToList();

        // 直接卸载所有需要卸载的房间
        foreach (var record in roomsToUnload)
        {
            UnloadRoom(record);
        }
    }

    /// <summary>
    /// 加载房间
    /// </summary>
    private void LoadRoom(SimpleRoomRecord record)
    {
        try
        {
            var room = RoomFactory?.Invoke(record);
            
            if (room != null)
            {
                room.Pos = record.Position;
                room.ORT = record.Orientation;
                room.Size = record.Size;
                
                // 完整初始化
                room.InitializeBuildingBlocks();
                
                record.LoadedRoom = room;
                record.LoadState = SimpleRoomLoadState.Loaded;
                record.LastAccessTime = Time.GetUnixTimeFromSystem();
                
                // GD.Print($"房间加载完成: {record.RoomId}");
            }
        }
        catch (Exception ex)
        {
            // GD.PrintErr($"房间加载失败: {record.RoomId} - {ex.Message}");
        }
    }

    /// <summary>
    /// 卸载房间
    /// </summary>
    private void UnloadRoom(SimpleRoomRecord record)
    {
        record.LoadedRoom = null;
        record.LoadState = SimpleRoomLoadState.Unloaded;
        
        // GD.Print($"房间卸载完成: {record.RoomId}");
    }

    /// <summary>
    /// 添加到空间索引
    /// </summary>
    private void AddToSpatialIndex(SimpleRoomRecord record)
    {
        var gridStart = new Vector2I(record.Area.Position.X / indexBlockSize, record.Area.Position.Y / indexBlockSize);
        var gridEnd = new Vector2I((record.Area.End.X - 1) / indexBlockSize, (record.Area.End.Y - 1) / indexBlockSize);
        
        for (int x = gridStart.X; x <= gridEnd.X; x++)
        {
            for (int y = gridStart.Y; y <= gridEnd.Y; y++)
            {
                var gridPos = new Vector2I(x, y);
                if (!spatialIndex.ContainsKey(gridPos))
                {
                    spatialIndex[gridPos] = new List<string>();
                }
                spatialIndex[gridPos].Add(record.RoomId);
            }
        }
    }

    /// <summary>
    /// 房间类型配置 - 集中管理房间类型相关配置
    /// 添加新房间类型时，只需要在这里修改这两个方法
    /// </summary>
    #region 房间类型配置

    /// <summary>
    /// 获取房间类型的默认尺寸
    /// </summary>
    public Vector2I GetDefaultSizeForRoomType(RoomType roomType)
    {
        return roomType switch
        {
            RoomType.SmallRoom => new Vector2I(10, 6),
            RoomType.LargeRoom => new Vector2I(30, 16),
            RoomType.BasicRoom => new Vector2I(22, 12),
            _ => new Vector2I(22, 12) // 默认尺寸
        };
    }

    /// <summary>
    /// 默认房间工厂 - 根据房间类型创建对应的房间对象
    /// </summary>
    private RoomBasic CreateDefaultRoom(SimpleRoomRecord record)
    {
        RoomBasic room = record.RoomType switch
        {
            RoomType.SmallRoom => new room_small(),
            RoomType.LargeRoom => new room_large(),
            RoomType.BasicRoom => new RoomBasic(),
            _ => new RoomBasic()
        };

        room.Size = record.Size;
        room.Message = $"{record.RoomType} ({record.RoomId})";

        return room;
    }

    #endregion

    /// <summary>
    /// 获取已加载房间数量
    /// </summary>
    private int GetLoadedRoomCount()
    {
        return roomRecords.Values.Count(r => r.LoadState == SimpleRoomLoadState.Loaded);
    }

    /// <summary>
    /// 获取房间
    /// </summary>
    public RoomBasic GetRoom(string roomId)
    {
        if (roomRecords.ContainsKey(roomId))
        {
            var record = roomRecords[roomId];
            record.LastAccessTime = Time.GetUnixTimeFromSystem();
            return record.LoadedRoom;
        }
        return null;
    }

    /// <summary>
    /// 获取指定位置的房间
    /// </summary>
    public RoomBasic GetRoomAtPosition(Vector2I position)
    {
        var intersecting = FindIntersectingRooms(new Rect2I(position, Vector2I.One));
        var loadedRoom = intersecting.FirstOrDefault(r => r.LoadState == SimpleRoomLoadState.Loaded);
        
        if (loadedRoom != null)
        {
            loadedRoom.LastAccessTime = Time.GetUnixTimeFromSystem();
            return loadedRoom.LoadedRoom;
        }
        
        return null;
    }

    /// <summary>
    /// 强制加载房间
    /// </summary>
    public void ForceLoadRoom(string roomId)
    {
        if (roomRecords.ContainsKey(roomId))
        {
            var record = roomRecords[roomId];
            if (record.LoadState == SimpleRoomLoadState.Unloaded)
            {
                LoadRoom(record);
            }
        }
    }

    /// <summary>
    /// 强制卸载房间
    /// </summary>
    public void ForceUnloadRoom(string roomId)
    {
        if (roomRecords.ContainsKey(roomId))
        {
            var record = roomRecords[roomId];
            if (record.LoadState == SimpleRoomLoadState.Loaded)
            {
                UnloadRoom(record);
            }
        }
    }

    /// <summary>
    /// 获取系统状态信息
    /// </summary>
    public Dictionary<string, object> GetSystemStatus()
    {
        var loadedCount = GetLoadedRoomCount();
        
        return new Dictionary<string, object>
        {
            ["TotalRooms"] = roomRecords.Count,
            ["LoadedRooms"] = loadedCount,
            ["SpatialIndexSize"] = spatialIndex.Count
        };
    }
}
