using Godot;
using System;
using System.Collections.Generic;

/// <summary>
/// 地图显示控件 - 显示缩小版的地图概览
/// 包含中心点、道路、建筑区域等信息
/// </summary>
public partial class MapDisplay : Control
{
    [Export] public Vector2 MapSize { get; set; } = new Vector2(500, 400); // 显示区域大小
    [Export] public Color BackgroundColor { get; set; } = Colors.DarkSlateGray;
    [Export] public Color CenterPointColor { get; set; } = Colors.Red;
    [Export] public Color GlobalRoadColor { get; set; } = Colors.Yellow;
    [Export] public Color BoundaryLineColor { get; set; } = Colors.LightGreen;
    [Export] public Color BuildingAreaColor { get; set; } = Colors.Orange;

    [Export] public Color SeaColor { get; set; } = Colors.Blue;
    
    private Map_res mapRes;
    private float scaleX;
    private float scaleY;
    private Vector2I mapBounds; // 地图实际尺寸
    private Vector2I mapOffset; // 地图偏移量
    
    // 缓存的绘制数据
    private List<Vector2> centerPoints = new List<Vector2>();
    private List<Vector2[]> globalRoadLines = new List<Vector2[]>();
    private List<Vector2[]> boundaryLines = new List<Vector2[]>(); // 边界顶点连线
    private List<Rect2> buildingAreas = new List<Rect2>(); // 建筑区域

    public override void _Ready()
    {
        SetCustomMinimumSize(MapSize);
        SetSize(MapSize);
    }

    /// <summary>
    /// 设置地图数据源
    /// </summary>
    public void SetMapData(Map_res mapRes)
    {
        this.mapRes = mapRes;
        CalculateScale();
        CacheDrawingData();
        QueueRedraw(); // 触发重绘
    }

    /// <summary>
    /// 计算缩放比例
    /// </summary>
    private void CalculateScale()
    {
        if (mapRes == null) return;

        // 计算地图实际尺寸
        mapBounds = new Vector2I(
            Math.Abs(mapRes.EdgeRight - mapRes.EdgeLeft),
            Math.Abs(mapRes.EdgeBottom - mapRes.EdgeTop)
        );

        mapOffset = new Vector2I(mapRes.EdgeLeft, mapRes.EdgeTop);

        // 计算缩放比例，保持宽高比
        float scaleByWidth = MapSize.X / mapBounds.X;
        float scaleByHeight = MapSize.Y / mapBounds.Y;
        float uniformScale = Math.Min(scaleByWidth, scaleByHeight) * 0.9f; // 留10%边距

        scaleX = uniformScale;
        scaleY = uniformScale;

        GD.Print($"地图显示缩放: {scaleX:F4}, 地图尺寸: {mapBounds}, 显示尺寸: {MapSize}");
    }

    /// <summary>
    /// 缓存绘制数据，避免每帧重新计算
    /// </summary>
    private void CacheDrawingData()
    {
        if (mapRes == null) return;

        centerPoints.Clear();
        globalRoadLines.Clear();
        boundaryLines.Clear();
        buildingAreas.Clear();

        // 缓存中心点
        foreach (var region in mapRes.reg_list)
        {
            Vector2 screenPos = WorldToScreen(region.central_coord);
            centerPoints.Add(screenPos);
        }

        // 缓存全局道路
        CacheGlobalRoads();

        // 缓存边界顶点
        CacheBoundaryLines();

        // 缓存建筑区域
        CacheBuildingAreas();

        GD.Print($"缓存完成: {centerPoints.Count}个中心点, {globalRoadLines.Count}条全局道路, {boundaryLines.Count}条边界线, {buildingAreas.Count}个建筑区域");
    }

    /// <summary>
    /// 缓存全局道路线段
    /// </summary>
    private void CacheGlobalRoads()
    {
        if (mapRes?.roadSystem == null) return;

        var roadSegments = mapRes.roadSystem.GetAllRoadSegments();
        foreach (var segment in roadSegments)
        {
            Vector2 start = WorldToScreen(segment.StartPoint);
            Vector2 end = WorldToScreen(segment.EndPoint);
            globalRoadLines.Add(new Vector2[] { start, end });
        }
    }

    /// <summary>
    /// 缓存边界顶点连线
    /// </summary>
    private void CacheBoundaryLines()
    {
        if (mapRes?.reg_list == null) return;

        foreach (var region in mapRes.reg_list)
        {
            if (region.BoundaryVertices == null || region.BoundaryVertices.Count < 3) continue;

            // 将边界顶点转换为屏幕坐标并连线
            var screenVertices = new List<Vector2>();
            foreach (var vertex in region.BoundaryVertices)
            {
                Vector2I worldVertex = new Vector2I((int)vertex.X, (int)vertex.Y);
                Vector2 screenVertex = WorldToScreen(worldVertex);
                screenVertices.Add(screenVertex);
            }

            // 创建边界线段（连接相邻顶点）
            for (int i = 0; i < screenVertices.Count; i++)
            {
                int nextIndex = (i + 1) % screenVertices.Count; // 最后一个点连接到第一个点
                boundaryLines.Add(new Vector2[] { screenVertices[i], screenVertices[nextIndex] });
            }
        }
    }

    /// <summary>
    /// 缓存建筑区域
    /// </summary>
    private void CacheBuildingAreas()
    {
        if (mapRes?.reg_list == null) return;

        foreach (var region in mapRes.reg_list)
        {
            if (region.BuildingAreas == null) continue;

            // 将每个建筑区域转换为屏幕坐标
            foreach (var buildingArea in region.BuildingAreas)
            {
                Rect2 screenRect = WorldRectToScreen(buildingArea);
                buildingAreas.Add(screenRect);
            }
        }
    }



    /// <summary>
    /// 世界坐标转屏幕坐标
    /// </summary>
    private Vector2 WorldToScreen(Vector2I worldPos)
    {
        Vector2I relativePos = worldPos - mapOffset;
        return new Vector2(
            relativePos.X * scaleX + (MapSize.X - mapBounds.X * scaleX) / 2,
            relativePos.Y * scaleY + (MapSize.Y - mapBounds.Y * scaleY) / 2
        );
    }

    /// <summary>
    /// 世界矩形转屏幕矩形
    /// </summary>
    private Rect2 WorldRectToScreen(Rect2I worldRect)
    {
        Vector2 topLeft = WorldToScreen(worldRect.Position);
        Vector2 bottomRight = WorldToScreen(worldRect.Position + worldRect.Size);
        return new Rect2(topLeft, bottomRight - topLeft);
    }

    /// <summary>
    /// 绘制地图
    /// </summary>
    public override void _Draw()
    {
        if (mapRes == null) return;

        // 绘制背景
        DrawRect(new Rect2(Vector2.Zero, MapSize), BackgroundColor);

        // 绘制海洋边界
        DrawSeaBorder();

        // 绘制建筑区域（在道路和边界线之前绘制，作为底层）
        foreach (var buildingArea in buildingAreas)
        {
            DrawRect(buildingArea, BuildingAreaColor, false, 1.0f); // 只绘制边框
        }

        // 绘制边界顶点连线（细线）
        foreach (var boundaryLine in boundaryLines)
        {
            DrawLine(boundaryLine[0], boundaryLine[1], BoundaryLineColor, 1.0f);
        }

        // 绘制全局道路
        foreach (var roadLine in globalRoadLines)
        {
            DrawLine(roadLine[0], roadLine[1], GlobalRoadColor, 2.0f);
        }

        // 绘制中心点（在最上层）
        foreach (var point in centerPoints)
        {
            DrawCircle(point, 3.0f, CenterPointColor);
        }

        // 绘制边框
        DrawRect(new Rect2(Vector2.Zero, MapSize), Colors.White, false, 2.0f);
    }

    /// <summary>
    /// 绘制海洋边界
    /// </summary>
    private void DrawSeaBorder()
    {
        int seaBorderWidth = 8; // 对应SetCharDir中的wid
        
        // 转换海洋边界到屏幕坐标
        Vector2 seaTopLeft = WorldToScreen(new Vector2I(mapRes.EdgeLeft, mapRes.EdgeTop));
        Vector2 seaBottomRight = WorldToScreen(new Vector2I(mapRes.EdgeRight, mapRes.EdgeBottom));

        Vector2 innerTopLeft = WorldToScreen(new Vector2I(mapRes.EdgeLeft + seaBorderWidth, mapRes.EdgeTop + seaBorderWidth));
        Vector2 innerBottomRight = WorldToScreen(new Vector2I(mapRes.EdgeRight - seaBorderWidth, mapRes.EdgeBottom - seaBorderWidth));
        
        // 绘制海洋区域（外边框）
        Rect2 outerRect = new Rect2(seaTopLeft, seaBottomRight - seaTopLeft);
        Rect2 innerRect = new Rect2(innerTopLeft, innerBottomRight - innerTopLeft);
        
        // 绘制四个海洋边框矩形
        // 上边框
        DrawRect(new Rect2(outerRect.Position, new Vector2(outerRect.Size.X, innerRect.Position.Y - outerRect.Position.Y)), SeaColor);
        // 下边框
        DrawRect(new Rect2(new Vector2(outerRect.Position.X, innerRect.Position.Y + innerRect.Size.Y), 
                          new Vector2(outerRect.Size.X, outerRect.Position.Y + outerRect.Size.Y - innerRect.Position.Y - innerRect.Size.Y)), SeaColor);
        // 左边框
        DrawRect(new Rect2(new Vector2(outerRect.Position.X, innerRect.Position.Y), 
                          new Vector2(innerRect.Position.X - outerRect.Position.X, innerRect.Size.Y)), SeaColor);
        // 右边框
        DrawRect(new Rect2(new Vector2(innerRect.Position.X + innerRect.Size.X, innerRect.Position.Y), 
                          new Vector2(outerRect.Position.X + outerRect.Size.X - innerRect.Position.X - innerRect.Size.X, innerRect.Size.Y)), SeaColor);
    }

    /// <summary>
    /// 刷新地图显示
    /// </summary>
    public void RefreshMap()
    {
        if (mapRes != null)
        {
            CacheDrawingData();
            QueueRedraw();
        }
    }

    /// <summary>
    /// 设置显示尺寸
    /// </summary>
    public void SetDisplaySize(Vector2 newSize)
    {
        MapSize = newSize;
        SetCustomMinimumSize(MapSize);
        SetSize(MapSize);
        CalculateScale();
        CacheDrawingData();
        QueueRedraw();
    }
}
